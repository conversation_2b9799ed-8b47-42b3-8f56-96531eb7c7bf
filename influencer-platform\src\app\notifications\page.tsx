'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth, useNotifications } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import {
  Bell,
  CheckCircle,
  Clock,
  DollarSign,
  MessageCircle,
  Star,
  AlertCircle,
  Trash2,
  MarkAsUnread,
  ArrowLeft
} from 'lucide-react'
import { formatDate } from '@/lib/utils'

export default function NotificationsPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const { notifications, markNotificationAsRead, clearNotifications } = useNotifications()
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all')

  // Mock notifications data
  const [mockNotifications] = useState([
    {
      id: '1',
      userId: '1',
      type: 'campaign_update' as const,
      title: 'تحديث حالة الحملة',
      message: 'تم قبول طلبك للحملة الإعلانية "منتج العناية بالبشرة"',
      isRead: false,
      actionUrl: '/merchant/campaigns/1',
      createdAt: new Date('2024-01-15T10:30:00')
    },
    {
      id: '2',
      userId: '1',
      type: 'payment' as const,
      title: 'تم استلام الدفعة',
      message: 'تم استلام دفعة بقيمة 2500 ريال للحملة الإعلانية',
      isRead: false,
      actionUrl: '/merchant/payments',
      createdAt: new Date('2024-01-15T09:15:00')
    },
    {
      id: '3',
      userId: '1',
      type: 'message' as const,
      title: 'رسالة جديدة',
      message: 'لديك رسالة جديدة من سارة أحمد',
      isRead: true,
      actionUrl: '/messages/1',
      createdAt: new Date('2024-01-14T16:45:00')
    },
    {
      id: '4',
      userId: '1',
      type: 'review' as const,
      title: 'تقييم جديد',
      message: 'تم إضافة تقييم جديد لحملتك الإعلانية',
      isRead: true,
      actionUrl: '/merchant/campaigns/2',
      createdAt: new Date('2024-01-14T14:20:00')
    },
    {
      id: '5',
      userId: '1',
      type: 'system' as const,
      title: 'تحديث المنصة',
      message: 'تم إضافة ميزات جديدة للمنصة. اكتشف المزيد!',
      isRead: true,
      actionUrl: '/updates',
      createdAt: new Date('2024-01-13T12:00:00')
    }
  ])

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }
  }, [isAuthenticated, router])

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'campaign_update':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'payment':
        return <DollarSign className="w-5 h-5 text-blue-500" />
      case 'message':
        return <MessageCircle className="w-5 h-5 text-purple-500" />
      case 'review':
        return <Star className="w-5 h-5 text-yellow-500" />
      case 'system':
        return <Bell className="w-5 h-5 text-gray-500" />
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'campaign_update':
        return 'bg-green-100'
      case 'payment':
        return 'bg-blue-100'
      case 'message':
        return 'bg-purple-100'
      case 'review':
        return 'bg-yellow-100'
      case 'system':
        return 'bg-gray-100'
      default:
        return 'bg-gray-100'
    }
  }

  const filteredNotifications = mockNotifications.filter(notification => {
    if (filter === 'unread') return !notification.isRead
    if (filter === 'read') return notification.isRead
    return true
  })

  const unreadCount = mockNotifications.filter(n => !n.isRead).length

  const handleNotificationClick = (notification: any) => {
    if (!notification.isRead) {
      markNotificationAsRead(notification.id)
    }
    if (notification.actionUrl) {
      router.push(notification.actionUrl)
    }
  }

  const markAllAsRead = () => {
    mockNotifications.forEach(notification => {
      if (!notification.isRead) {
        markNotificationAsRead(notification.id)
      }
    })
  }

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">الإشعارات</h1>
              {unreadCount > 0 && (
                <p className="text-sm text-gray-600">
                  {unreadCount} إشعار غير مقروء
                </p>
              )}
            </div>
          </div>

          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
            >
              تحديد الكل كمقروء
            </Button>
          )}
        </div>

        {/* Filter Tabs */}
        <Card>
          <div className="flex space-x-1 rtl:space-x-reverse bg-gray-100 rounded-lg p-1">
            {[
              { id: 'all', label: 'الكل', count: mockNotifications.length },
              { id: 'unread', label: 'غير مقروء', count: unreadCount },
              { id: 'read', label: 'مقروء', count: mockNotifications.length - unreadCount }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setFilter(tab.id as any)}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
                  filter === tab.id
                    ? 'bg-white text-green-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <span>{tab.label}</span>
                {tab.count > 0 && (
                  <Badge variant="default" size="sm" className="mr-2">
                    {tab.count}
                  </Badge>
                )}
              </button>
            ))}
          </div>
        </Card>

        {/* Notifications List */}
        <div className="space-y-3">
          {filteredNotifications.length > 0 ? (
            filteredNotifications.map((notification, index) => (
              <motion.div
                key={notification.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => handleNotificationClick(notification)}
                className="cursor-pointer"
              >
                <Card className={`transition-all hover:shadow-md ${
                  !notification.isRead ? 'border-l-4 border-l-green-500' : ''
                }`}>
                  <div className="flex items-start space-x-3 rtl:space-x-reverse">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                      getNotificationColor(notification.type)
                    }`}>
                      {getNotificationIcon(notification.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-1">
                        <h3 className={`font-medium ${
                          !notification.isRead ? 'text-gray-900' : 'text-gray-700'
                        }`}>
                          {notification.title}
                        </h3>
                        {!notification.isRead && (
                          <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0" />
                        )}
                      </div>

                      <p className={`text-sm mb-2 ${
                        !notification.isRead ? 'text-gray-700' : 'text-gray-600'
                      }`}>
                        {notification.message}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-gray-500">
                          <Clock className="w-3 h-3" />
                          <span>{formatDate(notification.createdAt)}</span>
                        </div>

                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          {notification.actionUrl && (
                            <span className="text-xs text-green-600 font-medium">
                              اضغط للعرض
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Bell className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                لا توجد إشعارات
              </h3>
              <p className="text-gray-600 mb-4">
                {filter === 'unread'
                  ? 'جميع الإشعارات مقروءة'
                  : filter === 'read'
                  ? 'لا توجد إشعارات مقروءة'
                  : 'ستظهر الإشعارات هنا عند وصولها'
                }
              </p>
            </motion.div>
          )}
        </div>

        {/* Clear All Button */}
        {filteredNotifications.length > 0 && (
          <div className="text-center pt-6">
            <Button
              variant="ghost"
              onClick={clearNotifications}
              leftIcon={<Trash2 className="w-4 h-4" />}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              مسح جميع الإشعارات
            </Button>
          </div>
        )}
      </div>
    </MobileLayout>
  )
}
