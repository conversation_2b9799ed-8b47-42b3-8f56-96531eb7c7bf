'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { 
  ArrowLeft,
  Filter,
  Search,
  Calendar,
  DollarSign,
  MapPin,
  Eye,
  MessageCircle,
  Clock,
  Star,
  CheckCircle,
  X
} from 'lucide-react'
import { formatPrice, formatDate } from '@/lib/utils'

export default function OpportunitiesPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [showFilters, setShowFilters] = useState(false)
  const [selectedFilter, setSelectedFilter] = useState('all')

  // Mock opportunities data
  const [opportunities] = useState([
    {
      id: '1',
      title: 'إعلان منتج العناية بالبشرة',
      merchantName: 'متجر الجمال',
      merchantAvatar: '',
      description: 'نبحث عن مؤثرة في مجال الجمال لتجربة منتجاتنا الجديدة وعمل ريل إنستغرام',
      budget: 2500,
      deadline: '2024-02-15',
      location: 'الرياض',
      category: 'beauty',
      requirements: ['ريل إنستغرام 30-60 ثانية', 'ستوري كامل', 'تجربة المنتج'],
      status: 'pending',
      postedDate: '2024-01-20',
      applicants: 12,
      isUrgent: false
    },
    {
      id: '2',
      title: 'مراجعة مطعم جديد',
      merchantName: 'مطعم الذواقة',
      merchantAvatar: '',
      description: 'مطعم جديد في جدة يبحث عن مؤثر طعام لزيارة المطعم وعمل محتوى',
      budget: 1800,
      deadline: '2024-02-10',
      location: 'جدة',
      category: 'food',
      requirements: ['زيارة المطعم', 'فيديو تيك توك', 'ستوري إنستغرام'],
      status: 'pending',
      postedDate: '2024-01-22',
      applicants: 8,
      isUrgent: true
    },
    {
      id: '3',
      title: 'إعلان تطبيق توصيل',
      merchantName: 'تطبيق سريع',
      merchantAvatar: '',
      description: 'تطبيق توصيل جديد يبحث عن مؤثرين لتجربة الخدمة وعمل محتوى ترويجي',
      budget: 3000,
      deadline: '2024-02-20',
      location: 'الدمام',
      category: 'technology',
      requirements: ['فيديو يوتيوب', 'ريل إنستغرام', 'سناب شات'],
      status: 'pending',
      postedDate: '2024-01-18',
      applicants: 15,
      isUrgent: false
    }
  ])

  const [filteredOpportunities, setFilteredOpportunities] = useState(opportunities)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, router])

  useEffect(() => {
    let filtered = opportunities

    if (selectedFilter !== 'all') {
      filtered = filtered.filter(opp => {
        switch (selectedFilter) {
          case 'urgent':
            return opp.isUrgent
          case 'high_budget':
            return opp.budget >= 2500
          case 'new':
            return new Date(opp.postedDate) > new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
          default:
            return true
        }
      })
    }

    setFilteredOpportunities(filtered)
  }, [selectedFilter, opportunities])

  const handleApply = (opportunityId: string) => {
    alert('تم إرسال طلبك بنجاح! سيتم التواصل معك قريباً.')
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'beauty': return '💄'
      case 'food': return '🍽️'
      case 'technology': return '📱'
      case 'fashion': return '👗'
      case 'travel': return '✈️'
      default: return '📦'
    }
  }

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'beauty': return 'الجمال'
      case 'food': return 'الطعام'
      case 'technology': return 'التقنية'
      case 'fashion': return 'الموضة'
      case 'travel': return 'السفر'
      default: return 'عام'
    }
  }

  if (!user) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">الفرص المتاحة</h1>
              <p className="text-gray-600">{filteredOpportunities.length} فرصة متاحة</p>
            </div>
          </div>
          
          <Button
            variant={showFilters ? 'primary' : 'outline'}
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            leftIcon={<Filter className="w-4 h-4" />}
          >
            فلتر
          </Button>
        </div>

        {/* Filters */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <Card>
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">الفلاتر</h3>
                <button
                  onClick={() => setShowFilters(false)}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  <X className="w-4 h-4 text-gray-500" />
                </button>
              </div>

              <div className="grid grid-cols-2 gap-2">
                {[
                  { key: 'all', label: 'الكل' },
                  { key: 'urgent', label: 'عاجل' },
                  { key: 'high_budget', label: 'ميزانية عالية' },
                  { key: 'new', label: 'جديد' }
                ].map((filter) => (
                  <button
                    key={filter.key}
                    onClick={() => setSelectedFilter(filter.key)}
                    className={`p-3 rounded-lg border text-sm transition-all ${
                      selectedFilter === filter.key
                        ? 'border-green-500 bg-green-50 text-green-600'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {filter.label}
                  </button>
                ))}
              </div>
            </Card>
          </motion.div>
        )}

        {/* Opportunities List */}
        <div className="space-y-4">
          {filteredOpportunities.map((opportunity, index) => (
            <motion.div
              key={opportunity.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 + index * 0.1 }}
            >
              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 rtl:space-x-reverse flex-1">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                        {opportunity.merchantName.charAt(0)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                          <h3 className="font-semibold text-gray-900 truncate">
                            {opportunity.title}
                          </h3>
                          {opportunity.isUrgent && (
                            <Badge variant="error" size="sm">
                              عاجل
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">
                          {opportunity.merchantName}
                        </p>
                        
                        <div className="flex items-center space-x-4 rtl:space-x-reverse text-xs text-gray-500">
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <MapPin className="w-3 h-3" />
                            <span>{opportunity.location}</span>
                          </div>
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <Calendar className="w-3 h-3" />
                            <span>{formatDate(opportunity.deadline)}</span>
                          </div>
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <span>{getCategoryIcon(opportunity.category)}</span>
                            <span>{getCategoryName(opportunity.category)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-left">
                      <div className="text-lg font-bold text-green-600">
                        {formatPrice(opportunity.budget)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {opportunity.applicants} متقدم
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-sm text-gray-700 leading-relaxed">
                    {opportunity.description}
                  </p>

                  {/* Requirements */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">المتطلبات:</h4>
                    <div className="flex flex-wrap gap-2">
                      {opportunity.requirements.map((req, idx) => (
                        <Badge key={idx} variant="default" size="sm">
                          {req}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-gray-500">
                      <Clock className="w-3 h-3" />
                      <span>نُشر {formatDate(opportunity.postedDate)}</span>
                    </div>
                    
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/creator/opportunities/${opportunity.id}`)}
                        leftIcon={<Eye className="w-4 h-4" />}
                      >
                        عرض
                      </Button>
                      
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => handleApply(opportunity.id)}
                        leftIcon={<CheckCircle className="w-4 h-4" />}
                      >
                        تقدم الآن
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {filteredOpportunities.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="text-gray-400 text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              لا توجد فرص متاحة
            </h3>
            <p className="text-gray-600 mb-4">
              جرب تغيير الفلاتر أو تحقق لاحقاً من الفرص الجديدة
            </p>
            <Button
              variant="outline"
              onClick={() => setSelectedFilter('all')}
            >
              مسح الفلاتر
            </Button>
          </motion.div>
        )}

        {/* Tips */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="bg-blue-50 border-blue-200">
            <div className="flex items-start space-x-3 rtl:space-x-reverse">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <Star className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-blue-900 mb-1">
                  نصائح للحصول على المزيد من الفرص
                </h3>
                <div className="text-sm text-blue-800 space-y-1">
                  <div>• أكمل ملفك الشخصي بالكامل</div>
                  <div>• أضف أعمالك السابقة إلى المعرض</div>
                  <div>• حافظ على معدل استجابة عالي</div>
                  <div>• قدم عروض أسعار تنافسية</div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
