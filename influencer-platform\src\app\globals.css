@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  --primary-green: #10B981;
  --primary-green-dark: #059669;
  --primary-green-light: #34D399;
  --secondary-blue: #3B82F6;
  --accent-purple: #8B5CF6;
  --text-dark: #f1f5f9;
  --text-gray: #cbd5e1;
  --text-light: #94a3b8;
  --bg-light: #0f172a;
  --bg-white: #1e293b;
  --border-light: #334155;
  --border-gray: #475569;
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --background: #0f172a;
  --foreground: #f1f5f9;
}

/* Dark mode is now default */
.dark {
  --primary-green: #10B981;
  --primary-green-dark: #059669;
  --primary-green-light: #34D399;
  --secondary-blue: #3B82F6;
  --accent-purple: #8B5CF6;
  --text-dark: #f1f5f9;
  --text-gray: #cbd5e1;
  --text-light: #94a3b8;
  --bg-light: #0f172a;
  --bg-white: #1e293b;
  --border-light: #334155;
  --border-gray: #475569;
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --background: #0f172a;
  --foreground: #f1f5f9;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: var(--font-geist-mono);
}

/* Light mode override (when explicitly set) */
.light {
  --primary-green: #10B981;
  --primary-green-dark: #059669;
  --primary-green-light: #34D399;
  --secondary-blue: #3B82F6;
  --accent-purple: #8B5CF6;
  --text-dark: #1F2937;
  --text-gray: #6B7280;
  --text-light: #9CA3AF;
  --bg-light: #F9FAFB;
  --bg-white: #FFFFFF;
  --border-light: #E5E7EB;
  --border-gray: #D1D5DB;
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --background: #F9FAFB;
  --foreground: #1F2937;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--text-dark);
  background-color: var(--bg-light);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-light);
}

::-webkit-scrollbar-thumb {
  background: var(--border-gray);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-gray);
}

/* App-like styles */
.app-container {
  max-width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
}

.mobile-container {
  max-width: 480px;
  margin: 0 auto;
  min-height: 100vh;
  background: var(--bg-white);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

@media (min-width: 769px) {
  .mobile-container {
    max-width: 420px;
  }
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-green), var(--primary-green-dark));
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.btn-secondary {
  background: white;
  color: var(--primary-green);
  border: 2px solid var(--primary-green);
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: var(--primary-green);
  color: white;
  transform: translateY(-1px);
}

/* Card styles */
.card {
  background: var(--bg-white);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* Input styles */
.input-field {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid var(--border-light);
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: var(--bg-white);
  color: var(--text-dark);
}

.input-field:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.input-field::placeholder {
  color: var(--text-light);
}

/* Arabic text support */
.rtl {
  direction: rtl;
  text-align: right;
}

/* Loading animation */
.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status badges */
.status-pending {
  background: #FEF3C7;
  color: #92400E;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-approved {
  background: #D1FAE5;
  color: #065F46;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-rejected {
  background: #FEE2E2;
  color: #991B1B;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-completed {
  background: #DBEAFE;
  color: #1E40AF;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}
