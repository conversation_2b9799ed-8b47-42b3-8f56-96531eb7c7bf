'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { ArrowLeft, Shield, Eye, Lock, Users } from 'lucide-react'

export default function PrivacyPage() {
  const router = useRouter()

  const sections = [
    {
      icon: Shield,
      title: 'حماية البيانات',
      content: 'نحن ملتزمون بحماية بياناتك الشخصية وفقاً لأعلى معايير الأمان والخصوصية. جميع البيانات مشفرة ومحمية بأحدث التقنيات.'
    },
    {
      icon: Eye,
      title: 'جمع البيانات',
      content: 'نجمع فقط البيانات الضرورية لتشغيل المنصة وتحسين خدماتنا. لا نشارك بياناتك مع أطراف ثالثة دون موافقتك الصريحة.'
    },
    {
      icon: Lock,
      title: 'تخزين البيانات',
      content: 'يتم تخزين جميع البيانات في خوادم آمنة داخل المملكة العربية السعودية مع نسخ احتياطية منتظمة.'
    },
    {
      icon: Users,
      title: 'حقوقك',
      content: 'لك الحق في الوصول إلى بياناتك، تعديلها، أو حذفها في أي وقت. يمكنك أيضاً سحب موافقتك على معالجة البيانات.'
    }
  ]

  return (
    <MobileLayout>
      <div className="min-h-screen bg-slate-900">
        <div className="max-w-4xl mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-slate-800 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-slate-400" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-white">سياسة الخصوصية</h1>
              <p className="text-slate-400">حماية بياناتك أولويتنا</p>
            </div>
          </div>

        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-green-600" />
              </div>
              <h2 className="text-xl font-semibold text-white mb-2">
                التزامنا بخصوصيتك
              </h2>
              <p className="text-slate-300 leading-relaxed">
                في منصة المؤثرين السعودية، نؤمن بأن خصوصيتك حق أساسي.
                هذه السياسة توضح كيف نجمع ونستخدم ونحمي معلوماتك الشخصية.
              </p>
            </div>
          </Card>
        </motion.div>

        {/* Sections */}
        <div className="space-y-4">
          {sections.map((section, index) => {
            const Icon = section.icon
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
              >
                <Card>
                  <div className="flex items-start space-x-4 rtl:space-x-reverse">
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Icon className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-white mb-2">
                        {section.title}
                      </h3>
                      <p className="text-slate-300 leading-relaxed">
                        {section.content}
                      </p>
                    </div>
                  </div>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Detailed Sections */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <h3 className="font-semibold text-white mb-4">البيانات التي نجمعها</h3>
            <div className="space-y-3 text-slate-300">
              <div>• معلومات الحساب: الاسم، البريد الإلكتروني، رقم الهاتف</div>
              <div>• معلومات الملف الشخصي: الصورة الشخصية، النبذة التعريفية</div>
              <div>• بيانات الاستخدام: كيفية تفاعلك مع المنصة</div>
              <div>• معلومات الدفع: تفاصيل المعاملات المالية (مشفرة)</div>
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card>
            <h3 className="font-semibold text-white mb-4">كيف نستخدم بياناتك</h3>
            <div className="space-y-3 text-slate-300">
              <div>• تشغيل وتحسين خدمات المنصة</div>
              <div>• التواصل معك بخصوص حسابك والخدمات</div>
              <div>• معالجة المدفوعات والمعاملات المالية</div>
              <div>• تحليل الاستخدام لتطوير المنصة</div>
              <div>• الامتثال للمتطلبات القانونية</div>
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card>
            <h3 className="font-semibold text-white mb-4">حقوقك</h3>
            <div className="space-y-3 text-slate-300">
              <div>• الوصول إلى بياناتك الشخصية</div>
              <div>• تصحيح أو تحديث معلوماتك</div>
              <div>• حذف حسابك وبياناتك</div>
              <div>• سحب الموافقة على معالجة البيانات</div>
              <div>• تقديم شكوى لدى الجهات المختصة</div>
            </div>
          </Card>
        </motion.div>

        {/* Contact */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
        >
          <Card className="bg-green-900/20 border-green-500/30">
            <div className="text-center">
              <h3 className="font-semibold text-green-400 mb-2">
                هل لديك أسئلة؟
              </h3>
              <p className="text-green-300 mb-4">
                إذا كان لديك أي استفسارات حول سياسة الخصوصية، لا تتردد في التواصل معنا
              </p>
              <Button
                variant="primary"
                size="sm"
                onClick={() => router.push('/contact')}
              >
                تواصل معنا
              </Button>
            </div>
          </Card>
        </motion.div>

        {/* Last Updated */}
        <div className="text-center text-sm text-slate-500">
          آخر تحديث: ديسمبر 2024
        </div>
        </div>
      </div>
    </MobileLayout>
  )
}
