'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'

export default function LoginPage() {
  const router = useRouter()
  const { login } = useAuth()

  const handleGuestLogin = (userType: 'merchant' | 'influencer' | 'ugc_creator' | 'admin') => {
    const guestUsers = {
      merchant: {
        id: 'guest-merchant',
        email: '<EMAIL>',
        phone: '+966501234567',
        name: 'تاجر تجريبي',
        type: 'merchant' as const,
        isVerified: true,
        createdAt: new Date().toISOString(),
        avatar: ''
      },
      influencer: {
        id: 'guest-influencer',
        email: '<EMAIL>',
        phone: '+966501234567',
        name: 'مؤثر تجريبي',
        type: 'influencer' as const,
        isVerified: true,
        createdAt: new Date().toISOString(),
        avatar: ''
      },
      ugc_creator: {
        id: 'guest-ugc',
        email: '<EMAIL>',
        phone: '+966501234567',
        name: 'مبدع تجريبي',
        type: 'ugc_creator' as const,
        isVerified: true,
        createdAt: new Date().toISOString(),
        avatar: ''
      },
      admin: {
        id: 'guest-admin',
        email: '<EMAIL>',
        phone: '+966501234567',
        name: 'مشرف تجريبي',
        type: 'admin' as const,
        isVerified: true,
        createdAt: new Date().toISOString(),
        avatar: ''
      }
    }

    const guestUser = guestUsers[userType]
    login(guestUser)

    // Redirect
    switch (userType) {
      case 'merchant':
        router.push('/merchant/dashboard')
        break
      case 'influencer':
      case 'ugc_creator':
        router.push('/creator/dashboard')
        break
      case 'admin':
        router.push('/admin/dashboard')
        break
      default:
        router.push('/')
    }
  }

  return (
    <MobileLayout showHeader={false} showBottomNav={false}>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          <Card>
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🇸🇦</span>
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">
                دخول سريع للتطوير
              </h1>
              <p className="text-slate-300">
                اختر نوع المستخدم للدخول مباشرة
              </p>
            </div>

            <div className="space-y-4">
              <Button
                variant="primary"
                size="lg"
                fullWidth
                onClick={() => handleGuestLogin('merchant')}
              >
                🏪 دخول كتاجر
              </Button>

              <Button
                variant="primary"
                size="lg"
                fullWidth
                onClick={() => handleGuestLogin('influencer')}
              >
                ⭐ دخول كمؤثر
              </Button>

              <Button
                variant="primary"
                size="lg"
                fullWidth
                onClick={() => handleGuestLogin('ugc_creator')}
              >
                🎨 دخول كمبدع
              </Button>

              <Button
                variant="primary"
                size="lg"
                fullWidth
                onClick={() => handleGuestLogin('admin')}
              >
                👨‍💼 دخول كمشرف
              </Button>
            </div>

            <div className="mt-6 text-center">
              <button
                onClick={() => router.push('/')}
                className="text-slate-400 hover:text-slate-200 text-sm"
              >
                العودة إلى الصفحة الرئيسية
              </button>
            </div>
          </Card>
        </div>
      </div>
    </MobileLayout>
  )
}
