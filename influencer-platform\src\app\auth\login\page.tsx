'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Card from '@/components/ui/Card'
import { Mail, Lock, Eye, EyeOff, ArrowRight } from 'lucide-react'
import { validateEmail } from '@/lib/utils'

export default function LoginPage() {
  const router = useRouter()
  const { login, setLoading } = useAuth()

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.email) {
      newErrors.email = 'البريد الإلكتروني مطلوب'
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }

    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة'
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)
    setLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mock user data - in real app, this would come from API
      const mockUser = {
        id: '1',
        email: formData.email,
        phone: '+966501234567',
        name: 'أحمد محمد',
        type: 'merchant' as const,
        isVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      login(mockUser)

      // Redirect based on user type
      switch (mockUser.type) {
        case 'merchant':
          router.push('/merchant/dashboard')
          break
        case 'influencer':
        case 'ugc_creator':
          router.push('/creator/dashboard')
          break
        case 'admin':
          router.push('/admin/dashboard')
          break
        default:
          router.push('/')
      }
    } catch (error) {
      setErrors({ general: 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.' })
    } finally {
      setIsLoading(false)
      setLoading(false)
    }
  }

  return (
    <MobileLayout showHeader={false} showBottomNav={false}>
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-md"
        >
          {/* Logo */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl">🇸🇦</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              مرحباً بك مرة أخرى
            </h1>
            <p className="text-gray-600">
              سجل الدخول للوصول إلى حسابك
            </p>
          </div>

          <Card>
            <form onSubmit={handleSubmit} className="space-y-6">
              {errors.general && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600 text-sm">{errors.general}</p>
                </div>
              )}

              <Input
                label="البريد الإلكتروني"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                error={errors.email}
                leftIcon={<Mail className="w-5 h-5" />}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />

              <Input
                label="كلمة المرور"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                error={errors.password}
                leftIcon={<Lock className="w-5 h-5" />}
                rightIcon={
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                }
                placeholder="كلمة المرور"
                disabled={isLoading}
              />

              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="mr-2 text-sm text-gray-600">تذكرني</span>
                </label>

                <button
                  type="button"
                  className="text-sm text-green-600 hover:text-green-700"
                  onClick={() => router.push('/auth/forgot-password')}
                >
                  نسيت كلمة المرور؟
                </button>
              </div>

              <Button
                type="submit"
                variant="primary"
                size="lg"
                fullWidth
                isLoading={isLoading}
                rightIcon={<ArrowRight className="w-5 h-5" />}
              >
                تسجيل الدخول
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-gray-600">
                ليس لديك حساب؟{' '}
                <button
                  onClick={() => router.push('/auth/register')}
                  className="text-green-600 hover:text-green-700 font-medium"
                >
                  إنشاء حساب جديد
                </button>
              </p>
            </div>
          </Card>

          <div className="mt-6 text-center">
            <button
              onClick={() => router.push('/')}
              className="text-gray-500 hover:text-gray-700 text-sm"
            >
              العودة إلى الصفحة الرئيسية
            </button>
          </div>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
