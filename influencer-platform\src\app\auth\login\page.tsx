'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Card from '@/components/ui/Card'
import { Mail, Lock, Eye, EyeOff, ArrowRight } from 'lucide-react'
import { validateEmail } from '@/lib/utils'

export default function LoginPage() {
  const router = useRouter()
  const { login, setLoading } = useAuth()

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.email) {
      newErrors.email = 'البريد الإلكتروني مطلوب'
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }

    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة'
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)
    setLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mock user data based on email - in real app, this would come from API
      let userType: 'merchant' | 'influencer' | 'ugc_creator' | 'admin' = 'influencer'
      let userName = 'مستخدم جديد'

      // Determine user type based on email
      if (formData.email.includes('merchant') || formData.email.includes('تاجر')) {
        userType = 'merchant'
        userName = 'أحمد التاجر'
      } else if (formData.email.includes('admin') || formData.email.includes('مشرف')) {
        userType = 'admin'
        userName = 'مشرف النظام'
      } else if (formData.email.includes('ugc') || formData.email.includes('مبدع')) {
        userType = 'ugc_creator'
        userName = 'سارة المبدعة'
      } else {
        userType = 'influencer'
        userName = 'محمد المؤثر'
      }

      const mockUser = {
        id: Date.now().toString(),
        email: formData.email,
        phone: '+966501234567',
        name: userName,
        type: userType,
        isVerified: true,
        createdAt: new Date().toISOString(),
        avatar: ''
      }

      login(mockUser)

      // Redirect based on user type
      switch (mockUser.type) {
        case 'merchant':
          router.push('/merchant/dashboard')
          break
        case 'influencer':
        case 'ugc_creator':
          router.push('/creator/dashboard')
          break
        case 'admin':
          router.push('/admin/dashboard')
          break
        default:
          router.push('/')
      }
    } catch (error) {
      setErrors({ general: 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.' })
    } finally {
      setIsLoading(false)
      setLoading(false)
    }
  }

  const handleGuestLogin = async (userType: 'merchant' | 'influencer' | 'ugc_creator' | 'admin') => {
    setIsLoading(true)
    setLoading(true)

    try {
      await new Promise(resolve => setTimeout(resolve, 1000))

      const guestUsers = {
        merchant: {
          id: 'guest-merchant',
          email: '<EMAIL>',
          phone: '+966501234567',
          name: 'تاجر تجريبي',
          type: 'merchant' as const,
          isVerified: true,
          createdAt: new Date().toISOString(),
          avatar: ''
        },
        influencer: {
          id: 'guest-influencer',
          email: '<EMAIL>',
          phone: '+966501234567',
          name: 'مؤثر تجريبي',
          type: 'influencer' as const,
          isVerified: true,
          createdAt: new Date().toISOString(),
          avatar: ''
        },
        ugc_creator: {
          id: 'guest-ugc',
          email: '<EMAIL>',
          phone: '+966501234567',
          name: 'مبدع تجريبي',
          type: 'ugc_creator' as const,
          isVerified: true,
          createdAt: new Date().toISOString(),
          avatar: ''
        },
        admin: {
          id: 'guest-admin',
          email: '<EMAIL>',
          phone: '+966501234567',
          name: 'مشرف تجريبي',
          type: 'admin' as const,
          isVerified: true,
          createdAt: new Date().toISOString(),
          avatar: ''
        }
      }

      const guestUser = guestUsers[userType]
      login(guestUser)

      // Redirect based on user type
      switch (userType) {
        case 'merchant':
          router.push('/merchant/dashboard')
          break
        case 'influencer':
        case 'ugc_creator':
          router.push('/creator/dashboard')
          break
        case 'admin':
          router.push('/admin/dashboard')
          break
        default:
          router.push('/')
      }
    } catch (error) {
      console.error('Guest login error:', error)
    } finally {
      setIsLoading(false)
      setLoading(false)
    }
  }

  return (
    <MobileLayout showHeader={false} showBottomNav={false}>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 flex items-center justify-center p-6">
        <div className="w-full max-w-6xl mx-auto flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="w-full max-w-md"
          >
          {/* Logo */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl">🇸🇦</span>
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">
              مرحباً بك مرة أخرى
            </h1>
            <p className="text-slate-300">
              سجل الدخول للوصول إلى حسابك
            </p>
          </div>

          <Card>
            <form onSubmit={handleSubmit} className="space-y-6">
              {errors.general && (
                <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                  <p className="text-red-400 text-sm">{errors.general}</p>
                </div>
              )}

              <Input
                label="البريد الإلكتروني"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                error={errors.email}
                leftIcon={<Mail className="w-5 h-5" />}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />

              <Input
                label="كلمة المرور"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                error={errors.password}
                leftIcon={<Lock className="w-5 h-5" />}
                rightIcon={
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-slate-400 hover:text-slate-200"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                }
                placeholder="كلمة المرور"
                disabled={isLoading}
              />

              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="rounded border-slate-600 bg-slate-700 text-green-500 focus:ring-green-500"
                  />
                  <span className="mr-2 text-sm text-slate-300">تذكرني</span>
                </label>

                <button
                  type="button"
                  className="text-sm text-green-400 hover:text-green-300"
                  onClick={() => router.push('/auth/forgot-password')}
                >
                  نسيت كلمة المرور؟
                </button>
              </div>

              <Button
                type="submit"
                variant="primary"
                size="lg"
                fullWidth
                isLoading={isLoading}
                rightIcon={<ArrowRight className="w-5 h-5" />}
              >
                تسجيل الدخول
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-slate-300">
                ليس لديك حساب؟{' '}
                <button
                  onClick={() => router.push('/auth/register')}
                  className="text-green-400 hover:text-green-300 font-medium"
                >
                  إنشاء حساب جديد
                </button>
              </p>
            </div>

            {/* Guest Login Section */}
            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-slate-600" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-4 bg-slate-800 text-slate-300">أو ادخل كضيف للتجربة</span>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleGuestLogin('merchant')}
                  disabled={isLoading}
                  className="text-xs"
                >
                  🏪 تاجر
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleGuestLogin('influencer')}
                  disabled={isLoading}
                  className="text-xs"
                >
                  ⭐ مؤثر
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleGuestLogin('ugc_creator')}
                  disabled={isLoading}
                  className="text-xs"
                >
                  🎨 مبدع
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleGuestLogin('admin')}
                  disabled={isLoading}
                  className="text-xs"
                >
                  👨‍💼 مشرف
                </Button>
              </div>
            </div>

            {/* Demo Instructions */}
            <div className="mt-4 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
              <h4 className="font-medium text-blue-400 mb-2">للتجربة العادية:</h4>
              <div className="text-sm text-blue-300 space-y-1">
                <div>• تاجر: استخدم بريد يحتوي على "merchant" أو "تاجر"</div>
                <div>• مؤثر: استخدم بريد يحتوي على "influencer" أو أي بريد آخر</div>
                <div>• مبدع: استخدم بريد يحتوي على "ugc" أو "مبدع"</div>
                <div>• مشرف: استخدم بريد يحتوي على "admin" أو "مشرف"</div>
                <div className="mt-2 text-xs text-blue-400">كلمة المرور: أي كلمة مرور (6 أحرف على الأقل)</div>
              </div>
            </div>
          </Card>

          <div className="mt-6 text-center">
            <button
              onClick={() => router.push('/')}
              className="text-slate-400 hover:text-slate-200 text-sm"
            >
              العودة إلى الصفحة الرئيسية
            </button>
          </div>
          </motion.div>
        </div>
      </div>
    </MobileLayout>
  )
}
