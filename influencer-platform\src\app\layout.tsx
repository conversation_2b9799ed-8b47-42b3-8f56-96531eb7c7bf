import type { Metadata } from "next";
import "./globals.css";
import ClientProvider from "@/components/providers/ClientProvider";

export const metadata: Metadata = {
  title: "منصة المؤثرين السعودية | اربط علامتك التجارية مع أفضل المؤثرين",
  description: "منصة سعودية متخصصة في ربط التجار مع المؤثرين ومبدعي المحتوى. دفع آمن، نتائج مضمونة، وخدمة عملاء متميزة.",
  keywords: "مؤثرين، تسويق، إعلانات، سناب شات، إنستغرام، تيك توك، السعودية",
  authors: [{ name: "منصة المؤثرين السعودية" }],
  creator: "منصة المؤثرين السعودية",
  publisher: "منصة المؤثرين السعودية",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://influencers-sa.com'),
  alternates: {
    canonical: '/',
    languages: {
      'ar-SA': '/ar',
      'en-US': '/en',
    },
  },
  openGraph: {
    title: "منصة المؤثرين السعودية",
    description: "اربط علامتك التجارية مع أفضل المؤثرين والمبدعين في المملكة",
    url: 'https://influencers-sa.com',
    siteName: 'منصة المؤثرين السعودية',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'منصة المؤثرين السعودية',
      },
    ],
    locale: 'ar_SA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "منصة المؤثرين السعودية",
    description: "اربط علامتك التجارية مع أفضل المؤثرين والمبدعين في المملكة",
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <meta name="theme-color" content="#10B981" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="منصة المؤثرين" />
        <link rel="apple-touch-icon" href="/icon.svg" />
        <link rel="icon" href="/icon.svg" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className="antialiased">
        <ClientProvider>
          {children}
        </ClientProvider>
      </body>
    </html>
  );
}
