export interface User {
  id: string
  email: string
  phone: string
  name: string
  avatar?: string
  type: 'merchant' | 'influencer' | 'ugc_creator' | 'admin'
  isVerified: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Merchant extends User {
  type: 'merchant'
  businessName: string
  businessType: string
  businessDescription?: string
  website?: string
  address?: string
  city: string
  campaigns: Campaign[]
}

export interface Influencer extends User {
  type: 'influencer' | 'ugc_creator'
  displayName: string
  bio: string
  city: string
  category: 'celebrity' | 'ugc_creator'
  platforms: PlatformStats[]
  services: Service[]
  portfolio: PortfolioItem[]
  audienceDemographics: AudienceDemographics
  rating: number
  totalCampaigns: number
  isAvailable: boolean
}

export interface PlatformStats {
  platform: 'snapchat' | 'instagram' | 'tiktok' | 'youtube' | 'twitter' | 'linkedin'
  username: string
  followers: number
  engagementRate: number
  isVerified: boolean
}

export interface Service {
  id: string
  type: 'store_visit' | 'full_story' | 'single_snap' | 'video' | 'instagram_reel' | 'tiktok_video' | 'youtube_video'
  name: string
  description: string
  price: number
  deliveryTime: number // in days
  isActive: boolean
}

export interface PortfolioItem {
  id: string
  type: 'image' | 'video'
  url: string
  thumbnail?: string
  title: string
  description?: string
  platform: string
  createdAt: Date
}

export interface AudienceDemographics {
  ageGroups: {
    '13-17': number
    '18-24': number
    '25-34': number
    '35-44': number
    '45-54': number
    '55+': number
  }
  gender: {
    male: number
    female: number
  }
  topCities: string[]
  interests: string[]
}

export interface Campaign {
  id: string
  merchantId: string
  influencerId?: string
  title: string
  description: string
  serviceType: string
  budget: number
  status: 'draft' | 'published' | 'pending' | 'approved' | 'in_progress' | 'completed' | 'rejected' | 'cancelled'
  requirements: string[]
  deliverables: string[]
  deadline: Date
  productImages: string[]
  storeLink?: string
  targetAudience?: {
    ageRange: string
    gender: string
    interests: string[]
    location: string
  }
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  review?: Review
}

export interface CampaignApplication {
  id: string
  campaignId: string
  influencerId: string
  proposedPrice: number
  message: string
  estimatedDelivery: Date
  status: 'pending' | 'approved' | 'rejected'
  createdAt: Date
}

export interface Review {
  id: string
  campaignId: string
  merchantId: string
  influencerId: string
  rating: number
  comment: string
  createdAt: Date
}

export interface Payment {
  id: string
  campaignId: string
  merchantId: string
  influencerId: string
  amount: number
  platformFee: number
  influencerAmount: number
  status: 'pending' | 'held' | 'released' | 'refunded'
  paymentMethod: 'apple_pay' | 'google_pay' | 'card'
  transactionId?: string
  createdAt: Date
  releasedAt?: Date
}

export interface Notification {
  id: string
  userId: string
  type: 'campaign_update' | 'payment' | 'message' | 'review' | 'system'
  title: string
  message: string
  isRead: boolean
  actionUrl?: string
  createdAt: Date
}

export interface Message {
  id: string
  campaignId: string
  senderId: string
  receiverId: string
  content: string
  type: 'text' | 'image' | 'file'
  isRead: boolean
  createdAt: Date
}

export interface AdminStats {
  totalUsers: number
  totalMerchants: number
  totalInfluencers: number
  totalCampaigns: number
  activeCampaigns: number
  completedCampaigns: number
  totalRevenue: number
  monthlyRevenue: number
  pendingPayments: number
  platformFees: number
}

export interface FilterOptions {
  city?: string
  category?: 'celebrity' | 'ugc_creator'
  platform?: string
  minFollowers?: number
  maxFollowers?: number
  minPrice?: number
  maxPrice?: number
  serviceType?: string
  interests?: string[]
  rating?: number
  availability?: boolean
}

export interface SearchFilters {
  query?: string
  city?: string
  category?: string
  platform?: string
  serviceType?: string
  priceRange?: [number, number]
  followersRange?: [number, number]
  rating?: number
  sortBy?: 'followers' | 'price' | 'rating' | 'recent'
  sortOrder?: 'asc' | 'desc'
}

export interface DashboardStats {
  totalCampaigns: number
  activeCampaigns: number
  completedCampaigns: number
  totalEarnings?: number
  totalSpent?: number
  averageRating?: number
  responseRate?: number
  completionRate?: number
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export type UserType = 'merchant' | 'influencer' | 'ugc_creator' | 'admin'
export type CampaignStatus = 'draft' | 'published' | 'pending' | 'approved' | 'in_progress' | 'completed' | 'rejected' | 'cancelled'
export type PaymentStatus = 'pending' | 'held' | 'released' | 'refunded'
export type Platform = 'snapchat' | 'instagram' | 'tiktok' | 'youtube' | 'twitter' | 'linkedin'
export type ServiceType = 'store_visit' | 'full_story' | 'single_snap' | 'video' | 'instagram_reel' | 'tiktok_video' | 'youtube_video'
