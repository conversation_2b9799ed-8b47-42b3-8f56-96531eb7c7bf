'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth, useSearch } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import {
  Search,
  Filter,
  MapPin,
  Users,
  Star,
  Eye,
  MessageCircle,
  Instagram,
  Camera
} from 'lucide-react'
import { formatNumber, formatPrice, getSaudiCities, getServiceTypes } from '@/lib/utils'

export default function SearchPage() {
  const router = useRouter()
  const { isAuthenticated } = useAuth()
  const { searchFilters, setSearchFilters } = useSearch()

  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Mock influencers data
  const [influencers] = useState([
    {
      id: '1',
      name: 'سارة أحمد',
      displayName: '<PERSON>',
      avatar: '/avatars/sarah.jpg',
      category: 'influencer',
      city: 'الرياض',
      bio: 'مؤثرة في مجال الموضة والجمال، أحب مشاركة أحدث صيحات الموضة',
      followers: {
        snapchat: 150000,
        instagram: 85000,
        tiktok: 200000
      },
      rating: 4.9,
      completedCampaigns: 45,
      services: [
        { type: 'instagram_reel', price: 2500 },
        { type: 'full_story', price: 1800 },
        { type: 'single_snap', price: 800 }
      ],
      interests: ['الموضة والأزياء', 'الجمال والعناية'],
      isVerified: true,
      isAvailable: true
    },
    {
      id: '2',
      name: 'محمد علي',
      displayName: 'Mohammed Ali',
      avatar: '/avatars/mohammed.jpg',
      category: 'influencer',
      city: 'جدة',
      bio: 'مؤثر في مجال التقنية والألعاب، أقدم مراجعات للأجهزة الحديثة',
      followers: {
        snapchat: 95000,
        instagram: 120000,
        tiktok: 180000,
        youtube: 75000
      },
      rating: 4.8,
      completedCampaigns: 32,
      services: [
        { type: 'youtube_video', price: 5000 },
        { type: 'tiktok_video', price: 3000 },
        { type: 'instagram_reel', price: 2200 }
      ],
      interests: ['التقنية والألعاب', 'الأعمال والمال'],
      isVerified: true,
      isAvailable: true
    },
    {
      id: '3',
      name: 'نورا خالد',
      displayName: 'Nora Khalid',
      avatar: '/avatars/nora.jpg',
      category: 'ugc_creator',
      city: 'الدمام',
      bio: 'مبدعة محتوى متخصصة في الطعام والمطاعم، أحب تجربة الأطباق الجديدة',
      followers: {
        snapchat: 45000,
        instagram: 35000,
        tiktok: 60000
      },
      rating: 4.7,
      completedCampaigns: 28,
      services: [
        { type: 'store_visit', price: 1500 },
        { type: 'tiktok_video', price: 1200 },
        { type: 'full_story', price: 1000 }
      ],
      interests: ['الطعام والمطاعم', 'السفر والسياحة'],
      isVerified: false,
      isAvailable: true
    }
  ])

  const [filteredInfluencers, setFilteredInfluencers] = useState(influencers)

  useEffect(() => {
    // Apply filters
    let filtered = influencers

    if (searchQuery) {
      filtered = filtered.filter(influencer =>
        influencer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        influencer.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        influencer.bio.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    if (searchFilters.city) {
      filtered = filtered.filter(influencer => influencer.city === searchFilters.city)
    }

    if (searchFilters.category) {
      filtered = filtered.filter(influencer => influencer.category === searchFilters.category)
    }

    if (searchFilters.minFollowers) {
      filtered = filtered.filter(influencer => {
        const totalFollowers = Object.values(influencer.followers).reduce((sum, count) => sum + count, 0)
        return totalFollowers >= searchFilters.minFollowers
      })
    }

    setFilteredInfluencers(filtered)
  }, [searchQuery, searchFilters, influencers])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  const handleFilterChange = (key: string, value: any) => {
    setSearchFilters({
      ...searchFilters,
      [key]: value
    })
  }

  const clearFilters = () => {
    setSearchFilters({})
    setSearchQuery('')
  }

  const cities = getSaudiCities()
  const serviceTypes = getServiceTypes()

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            اكتشف المؤثرين
          </h1>
          <p className="text-gray-600">
            ابحث عن المؤثر المناسب لعلامتك التجارية
          </p>
        </div>

        {/* Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="flex space-x-3 rtl:space-x-reverse">
            <div className="flex-1">
              <Input
                placeholder="ابحث عن مؤثر..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                leftIcon={<Search className="w-5 h-5" />}
              />
            </div>
            <Button
              variant={showFilters ? 'primary' : 'outline'}
              onClick={() => setShowFilters(!showFilters)}
              leftIcon={<Filter className="w-5 h-5" />}
            >
              فلتر
            </Button>
          </div>
        </motion.div>

        {/* Filters */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <Card>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">الفلاتر</h3>
                  <button
                    onClick={clearFilters}
                    className="text-sm text-green-600 hover:text-green-700"
                  >
                    مسح الكل
                  </button>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المدينة
                    </label>
                    <select
                      value={searchFilters.city || ''}
                      onChange={(e) => handleFilterChange('city', e.target.value)}
                      className="input-field"
                    >
                      <option value="">جميع المدن</option>
                      {cities.map(city => (
                        <option key={city} value={city}>{city}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نوع المؤثر
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => handleFilterChange('category', 'influencer')}
                        className={`p-3 rounded-lg border text-sm transition-all ${
                          searchFilters.category === 'influencer'
                            ? 'border-green-500 bg-green-50 text-green-600'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        مؤثر مشهور
                      </button>
                      <button
                        onClick={() => handleFilterChange('category', 'ugc_creator')}
                        className={`p-3 rounded-lg border text-sm transition-all ${
                          searchFilters.category === 'ugc_creator'
                            ? 'border-green-500 bg-green-50 text-green-600'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        مبدع محتوى
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Results Count */}
        <div className="flex items-center justify-between">
          <p className="text-gray-600">
            {filteredInfluencers.length} مؤثر متاح
          </p>
          {(searchQuery || Object.keys(searchFilters).length > 0) && (
            <button
              onClick={clearFilters}
              className="text-sm text-green-600 hover:text-green-700"
            >
              مسح البحث
            </button>
          )}
        </div>

        {/* Influencers Grid */}
        <div className="space-y-4">
          {filteredInfluencers.map((influencer, index) => (
            <motion.div
              key={influencer.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 + index * 0.1 }}
            >
              <Card
                className="cursor-pointer"
                onClick={() => router.push(`/influencer/${influencer.id}`)}
              >
                <div className="flex items-start space-x-4 rtl:space-x-reverse">
                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {influencer.name.charAt(0)}
                    </div>
                    {influencer.isVerified && (
                      <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                      <h3 className="font-semibold text-gray-900 truncate">
                        {influencer.name}
                      </h3>
                      <Badge
                        variant={influencer.category === 'influencer' ? 'info' : 'default'}
                        size="sm"
                      >
                        {influencer.category === 'influencer' ? 'مؤثر' : 'مبدع محتوى'}
                      </Badge>
                    </div>

                    <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 mb-2">
                      <MapPin className="w-4 h-4" />
                      <span>{influencer.city}</span>
                      <span>•</span>
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span>{influencer.rating}</span>
                      <span>•</span>
                      <span>{influencer.completedCampaigns} حملة</span>
                    </div>

                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {influencer.bio}
                    </p>

                    {/* Platform Stats */}
                    <div className="flex items-center space-x-4 rtl:space-x-reverse mb-3">
                      {Object.entries(influencer.followers).map(([platform, count]) => (
                        <div key={platform} className="flex items-center space-x-1 rtl:space-x-reverse text-xs text-gray-500">
                          <span>
                            {platform === 'snapchat' ? '👻' :
                             platform === 'instagram' ? '📷' :
                             platform === 'tiktok' ? '🎵' : '📺'}
                          </span>
                          <span>{formatNumber(count)}</span>
                        </div>
                      ))}
                    </div>

                    {/* Services */}
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        من {formatPrice(Math.min(...influencer.services.map(s => s.price)))}
                      </div>

                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            router.push(`/influencer/${influencer.id}`)
                          }}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>

                        {isAuthenticated && (
                          <Button
                            variant="primary"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              router.push(`/influencer/${influencer.id}/contact`)
                            }}
                          >
                            تواصل
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {filteredInfluencers.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="text-gray-400 text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              لا توجد نتائج
            </h3>
            <p className="text-gray-600 mb-4">
              جرب تغيير معايير البحث أو الفلاتر
            </p>
            <Button
              variant="outline"
              onClick={clearFilters}
            >
              مسح الفلاتر
            </Button>
          </motion.div>
        )}
      </div>
    </MobileLayout>
  )
}
