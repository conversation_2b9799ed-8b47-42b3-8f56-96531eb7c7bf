import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, Influencer, Merchant, Campaign, Notification, SearchFilters } from './types'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (user: User) => void
  logout: () => void
  updateUser: (updates: Partial<User>) => void
  setLoading: (loading: boolean) => void
}

interface AppState {
  // Navigation
  currentPage: string
  setCurrentPage: (page: string) => void
  
  // Search and Filters
  searchFilters: SearchFilters
  setSearchFilters: (filters: SearchFilters) => void
  clearFilters: () => void
  
  // Influencers
  influencers: Influencer[]
  setInfluencers: (influencers: Influencer[]) => void
  addInfluencer: (influencer: Influencer) => void
  updateInfluencer: (id: string, updates: Partial<Influencer>) => void
  
  // Campaigns
  campaigns: Campaign[]
  setCampaigns: (campaigns: Campaign[]) => void
  addCampaign: (campaign: Campaign) => void
  updateCampaign: (id: string, updates: Partial<Campaign>) => void
  deleteCampaign: (id: string) => void
  
  // Notifications
  notifications: Notification[]
  setNotifications: (notifications: Notification[]) => void
  addNotification: (notification: Notification) => void
  markNotificationAsRead: (id: string) => void
  clearNotifications: () => void
  
  // UI State
  isMobileMenuOpen: boolean
  setMobileMenuOpen: (open: boolean) => void
  isLoading: boolean
  setLoading: (loading: boolean) => void
  error: string | null
  setError: (error: string | null) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      
      login: (user: User) => {
        set({ user, isAuthenticated: true, isLoading: false })
      },
      
      logout: () => {
        set({ user: null, isAuthenticated: false, isLoading: false })
      },
      
      updateUser: (updates: Partial<User>) => {
        const currentUser = get().user
        if (currentUser) {
          set({ user: { ...currentUser, ...updates } })
        }
      },
      
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

export const useAppStore = create<AppState>((set, get) => ({
  // Navigation
  currentPage: 'home',
  setCurrentPage: (page: string) => set({ currentPage: page }),
  
  // Search and Filters
  searchFilters: {},
  setSearchFilters: (filters: SearchFilters) => set({ searchFilters: filters }),
  clearFilters: () => set({ searchFilters: {} }),
  
  // Influencers
  influencers: [],
  setInfluencers: (influencers: Influencer[]) => set({ influencers }),
  addInfluencer: (influencer: Influencer) => {
    const currentInfluencers = get().influencers
    set({ influencers: [...currentInfluencers, influencer] })
  },
  updateInfluencer: (id: string, updates: Partial<Influencer>) => {
    const currentInfluencers = get().influencers
    const updatedInfluencers = currentInfluencers.map(influencer =>
      influencer.id === id ? { ...influencer, ...updates } : influencer
    )
    set({ influencers: updatedInfluencers })
  },
  
  // Campaigns
  campaigns: [],
  setCampaigns: (campaigns: Campaign[]) => set({ campaigns }),
  addCampaign: (campaign: Campaign) => {
    const currentCampaigns = get().campaigns
    set({ campaigns: [...currentCampaigns, campaign] })
  },
  updateCampaign: (id: string, updates: Partial<Campaign>) => {
    const currentCampaigns = get().campaigns
    const updatedCampaigns = currentCampaigns.map(campaign =>
      campaign.id === id ? { ...campaign, ...updates } : campaign
    )
    set({ campaigns: updatedCampaigns })
  },
  deleteCampaign: (id: string) => {
    const currentCampaigns = get().campaigns
    const filteredCampaigns = currentCampaigns.filter(campaign => campaign.id !== id)
    set({ campaigns: filteredCampaigns })
  },
  
  // Notifications
  notifications: [],
  setNotifications: (notifications: Notification[]) => set({ notifications }),
  addNotification: (notification: Notification) => {
    const currentNotifications = get().notifications
    set({ notifications: [notification, ...currentNotifications] })
  },
  markNotificationAsRead: (id: string) => {
    const currentNotifications = get().notifications
    const updatedNotifications = currentNotifications.map(notification =>
      notification.id === id ? { ...notification, isRead: true } : notification
    )
    set({ notifications: updatedNotifications })
  },
  clearNotifications: () => set({ notifications: [] }),
  
  // UI State
  isMobileMenuOpen: false,
  setMobileMenuOpen: (open: boolean) => set({ isMobileMenuOpen: open }),
  isLoading: false,
  setLoading: (loading: boolean) => set({ isLoading: loading }),
  error: null,
  setError: (error: string | null) => set({ error: error }),
}))

// Selectors for better performance
export const useAuth = () => useAuthStore((state) => ({
  user: state.user,
  isAuthenticated: state.isAuthenticated,
  isLoading: state.isLoading,
  login: state.login,
  logout: state.logout,
  updateUser: state.updateUser,
  setLoading: state.setLoading,
}))

export const useInfluencers = () => useAppStore((state) => ({
  influencers: state.influencers,
  setInfluencers: state.setInfluencers,
  addInfluencer: state.addInfluencer,
  updateInfluencer: state.updateInfluencer,
}))

export const useCampaigns = () => useAppStore((state) => ({
  campaigns: state.campaigns,
  setCampaigns: state.setCampaigns,
  addCampaign: state.addCampaign,
  updateCampaign: state.updateCampaign,
  deleteCampaign: state.deleteCampaign,
}))

export const useNotifications = () => useAppStore((state) => ({
  notifications: state.notifications,
  setNotifications: state.setNotifications,
  addNotification: state.addNotification,
  markNotificationAsRead: state.markNotificationAsRead,
  clearNotifications: state.clearNotifications,
}))

export const useUI = () => useAppStore((state) => ({
  currentPage: state.currentPage,
  setCurrentPage: state.setCurrentPage,
  isMobileMenuOpen: state.isMobileMenuOpen,
  setMobileMenuOpen: state.setMobileMenuOpen,
  isLoading: state.isLoading,
  setLoading: state.setLoading,
  error: state.error,
  setError: state.setError,
}))

export const useSearch = () => useAppStore((state) => ({
  searchFilters: state.searchFilters,
  setSearchFilters: state.setSearchFilters,
  clearFilters: state.clearFilters,
}))
