'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '@/lib/hooks'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import {
  TrendingUp,
  DollarSign,
  Clock,
  CheckCircle,
  Eye,
  MessageCircle,
  Star,
  Calendar,
  Users,
  Camera,
  Settings
} from 'lucide-react'
import { formatPrice, formatDate, getStatusColor, getStatusText, formatNumber } from '@/lib/utils'

export default function CreatorDashboard() {
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)

  // Mock data - in real app, this would come from API
  const [stats, setStats] = useState({
    totalEarnings: 25000,
    activeCampaigns: 5,
    completedCampaigns: 23,
    averageRating: 4.9,
    responseRate: 98,
    completionRate: 96
  })

  const [platformStats, setPlatformStats] = useState([
    { platform: 'snapchat', followers: 150000, engagement: 8.5 },
    { platform: 'instagram', followers: 85000, engagement: 6.2 },
    { platform: 'tiktok', followers: 200000, engagement: 12.3 }
  ])

  const [recentOpportunities, setRecentOpportunities] = useState([
    {
      id: '1',
      title: 'إعلان منتج العناية بالبشرة',
      merchantName: 'متجر الجمال',
      budget: 2500,
      deadline: new Date('2024-02-15'),
      platform: 'instagram',
      serviceType: 'instagram_reel',
      status: 'pending'
    },
    {
      id: '2',
      title: 'تجربة مطعم جديد',
      merchantName: 'مطعم الذواقة',
      budget: 1800,
      deadline: new Date('2024-02-12'),
      platform: 'snapchat',
      serviceType: 'store_visit',
      status: 'approved'
    },
    {
      id: '3',
      title: 'مراجعة تطبيق توصيل',
      merchantName: 'شركة التوصيل السريع',
      budget: 3000,
      deadline: new Date('2024-02-20'),
      platform: 'tiktok',
      serviceType: 'tiktok_video',
      status: 'in_progress'
    }
  ])

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }

    if (user?.type !== 'influencer' && user?.type !== 'ugc_creator') {
      router.push('/')
      return
    }

    // Simulate loading
    setTimeout(() => setIsLoading(false), 1000)
  }, [isAuthenticated, user, router])

  if (isLoading) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            مرحباً، {user?.name}
          </h1>
          <p className="text-gray-600">
            إليك نظرة عامة على أدائك وفرصك
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {formatPrice(stats.totalEarnings)}
              </div>
              <div className="text-sm text-gray-600">
                إجمالي الأرباح
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stats.activeCampaigns}
              </div>
              <div className="text-sm text-gray-600">
                حملات نشطة
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="w-6 h-6 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stats.completedCampaigns}
              </div>
              <div className="text-sm text-gray-600">
                حملات مكتملة
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stats.averageRating}
              </div>
              <div className="text-sm text-gray-600">
                متوسط التقييم
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Platform Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                إحصائيات المنصات
              </h2>
              <Button
                variant="ghost"
                size="sm"
                leftIcon={<Settings className="w-4 h-4" />}
                onClick={() => router.push('/creator/profile')}
              >
                تحديث
              </Button>
            </div>

            <div className="space-y-3">
              {platformStats.map((platform, index) => (
                <div key={platform.platform} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-purple-500 rounded-lg flex items-center justify-center">
                      <span className="text-white text-sm font-bold">
                        {platform.platform === 'snapchat' ? '👻' :
                         platform.platform === 'instagram' ? '📷' : '🎵'}
                      </span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 capitalize">
                        {platform.platform}
                      </div>
                      <div className="text-sm text-gray-600">
                        {formatNumber(platform.followers)} متابع
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {platform.engagement}%
                    </div>
                    <div className="text-xs text-gray-500">
                      معدل التفاعل
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              إجراءات سريعة
            </h2>

            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="primary"
                size="sm"
                leftIcon={<Eye className="w-4 h-4" />}
                onClick={() => router.push('/creator/opportunities')}
                className="text-sm"
              >
                الفرص المتاحة
              </Button>

              <Button
                variant="secondary"
                size="sm"
                leftIcon={<Camera className="w-4 h-4" />}
                onClick={() => router.push('/creator/portfolio')}
                className="text-sm"
              >
                إدارة المعرض
              </Button>
            </div>
          </Card>
        </motion.div>

        {/* Recent Opportunities */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                الفرص الأخيرة
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/creator/opportunities')}
              >
                عرض الكل
              </Button>
            </div>

            <div className="space-y-4">
              {recentOpportunities.map((opportunity, index) => (
                <motion.div
                  key={opportunity.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8 + index * 0.1 }}
                  className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onClick={() => router.push(`/creator/opportunities/${opportunity.id}`)}
                >
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                    {opportunity.merchantName.charAt(0)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900 truncate">
                      {opportunity.title}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {opportunity.merchantName} • {formatPrice(opportunity.budget)}
                    </p>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                      <Badge
                        variant={
                          opportunity.status === 'approved' ? 'success' :
                          opportunity.status === 'in_progress' ? 'info' :
                          opportunity.status === 'pending' ? 'warning' : 'default'
                        }
                        size="sm"
                      >
                        {getStatusText(opportunity.status)}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {formatDate(opportunity.deadline)}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <button className="p-2 text-gray-400 hover:text-gray-600">
                      <MessageCircle className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600">
                      <Eye className="w-4 h-4" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </Card>
        </motion.div>

        {/* Performance Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
        >
          <Card>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              مؤشرات الأداء
            </h2>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">معدل الاستجابة</span>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-20 h-2 bg-gray-200 rounded-full">
                    <div
                      className="h-full bg-green-500 rounded-full"
                      style={{ width: `${stats.responseRate}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {stats.responseRate}%
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-600">معدل الإنجاز</span>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-20 h-2 bg-gray-200 rounded-full">
                    <div
                      className="h-full bg-blue-500 rounded-full"
                      style={{ width: `${stats.completionRate}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {stats.completionRate}%
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
