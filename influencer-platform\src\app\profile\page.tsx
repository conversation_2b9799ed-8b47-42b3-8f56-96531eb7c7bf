'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Edit3,
  Save,
  X,
  Camera,
  Shield,
  Star,
  Calendar,
  Settings
} from 'lucide-react'

export default function ProfilePage() {
  const router = useRouter()
  const { user, isAuthenticated, updateUser } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    bio: '',
    city: ''
  })

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }

    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        bio: (user as any).bio || '',
        city: (user as any).city || ''
      })
    }
  }, [isAuthenticated, user, router])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Update user data
      updateUser({
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        ...(user?.type === 'influencer' || user?.type === 'ugc_creator' ? {
          bio: formData.bio,
          city: formData.city
        } : {})
      })

      setIsEditing(false)
    } catch (error) {
      console.error('Error updating profile:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        bio: (user as any).bio || '',
        city: (user as any).city || ''
      })
    }
    setIsEditing(false)
  }

  if (!user) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }

  const getUserTypeLabel = (type: string) => {
    switch (type) {
      case 'merchant': return 'تاجر'
      case 'influencer': return 'مؤثر'
      case 'ugc_creator': return 'مبدع محتوى'
      case 'admin': return 'مشرف'
      default: return 'مستخدم'
    }
  }

  const getUserTypeColor = (type: string) => {
    switch (type) {
      case 'merchant': return 'info'
      case 'influencer': return 'success'
      case 'ugc_creator': return 'warning'
      case 'admin': return 'error'
      default: return 'default'
    }
  }

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            الملف الشخصي
          </h1>
          <p className="text-gray-600">
            إدارة معلوماتك الشخصية
          </p>
        </div>

        {/* Profile Picture & Basic Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <div className="text-center">
              {/* Profile Picture */}
              <div className="relative inline-block mb-4">
                <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center text-white font-bold text-2xl">
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    user.name.charAt(0)
                  )}
                </div>
                <button className="absolute bottom-0 right-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors">
                  <Camera className="w-4 h-4" />
                </button>
              </div>

              {/* Name & Type */}
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                {user.name}
              </h2>

              <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse mb-4">
                <Badge
                  variant={getUserTypeColor(user.type) as any}
                  size="sm"
                >
                  {getUserTypeLabel(user.type)}
                </Badge>

                {user.isVerified && (
                  <Badge variant="success" size="sm">
                    <Shield className="w-3 h-3 mr-1" />
                    موثق
                  </Badge>
                )}
              </div>

              {/* Join Date */}
              <div className="flex items-center justify-center text-sm text-gray-500">
                <Calendar className="w-4 h-4 mr-1" />
                <span>انضم في {new Date(user.createdAt).toLocaleDateString('ar-SA')}</span>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Profile Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                المعلومات الشخصية
              </h3>

              {!isEditing ? (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                  leftIcon={<Edit3 className="w-4 h-4" />}
                >
                  تعديل
                </Button>
              ) : (
                <div className="flex space-x-2 rtl:space-x-reverse">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCancel}
                    leftIcon={<X className="w-4 h-4" />}
                  >
                    إلغاء
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleSave}
                    isLoading={isLoading}
                    leftIcon={<Save className="w-4 h-4" />}
                  >
                    حفظ
                  </Button>
                </div>
              )}
            </div>

            <div className="space-y-4">
              {isEditing ? (
                <>
                  <Input
                    label="الاسم الكامل"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    leftIcon={<User className="w-5 h-5" />}
                  />

                  <Input
                    label="البريد الإلكتروني"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    leftIcon={<Mail className="w-5 h-5" />}
                  />

                  <Input
                    label="رقم الجوال"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    leftIcon={<Phone className="w-5 h-5" />}
                  />

                  {(user.type === 'influencer' || user.type === 'ugc_creator') && (
                    <>
                      <Input
                        label="المدينة"
                        value={formData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        leftIcon={<MapPin className="w-5 h-5" />}
                      />

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          النبذة التعريفية
                        </label>
                        <textarea
                          value={formData.bio}
                          onChange={(e) => handleInputChange('bio', e.target.value)}
                          className="input-field min-h-[100px] resize-none"
                          placeholder="اكتب نبذة مختصرة عن نفسك..."
                        />
                      </div>
                    </>
                  )}
                </>
              ) : (
                <>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <User className="w-5 h-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">الاسم</div>
                      <div className="font-medium text-gray-900">{user.name}</div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Mail className="w-5 h-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">البريد الإلكتروني</div>
                      <div className="font-medium text-gray-900">{user.email}</div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">رقم الجوال</div>
                      <div className="font-medium text-gray-900">{user.phone}</div>
                    </div>
                  </div>

                  {(user.type === 'influencer' || user.type === 'ugc_creator') && (
                    <>
                      {(user as any).city && (
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <MapPin className="w-5 h-5 text-gray-400" />
                          <div>
                            <div className="text-sm text-gray-500">المدينة</div>
                            <div className="font-medium text-gray-900">{(user as any).city}</div>
                          </div>
                        </div>
                      )}

                      {(user as any).bio && (
                        <div>
                          <div className="text-sm text-gray-500 mb-1">النبذة التعريفية</div>
                          <div className="text-gray-900 leading-relaxed">{(user as any).bio}</div>
                        </div>
                      )}
                    </>
                  )}
                </>
              )}
            </div>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              إجراءات سريعة
            </h3>

            <div className="space-y-3">
              <Button
                variant="outline"
                fullWidth
                leftIcon={<Settings className="w-5 h-5" />}
                onClick={() => router.push('/settings')}
              >
                إعدادات الحساب
              </Button>

              {user.type === 'influencer' || user.type === 'ugc_creator' ? (
                <Button
                  variant="outline"
                  fullWidth
                  leftIcon={<Star className="w-5 h-5" />}
                  onClick={() => router.push('/creator/profile')}
                >
                  إدارة الملف المهني
                </Button>
              ) : (
                <Button
                  variant="outline"
                  fullWidth
                  leftIcon={<User className="w-5 h-5" />}
                  onClick={() => router.push('/merchant/profile')}
                >
                  إعدادات النشاط التجاري
                </Button>
              )}

              <Button
                variant="ghost"
                fullWidth
                className="text-red-600 hover:bg-red-50"
                onClick={() => {
                  if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    logout()
                    router.push('/auth/login')
                  }
                }}
              >
                تسجيل الخروج
              </Button>
            </div>
          </Card>
        </motion.div>

        {/* Account Stats (for influencers) */}
        {(user.type === 'influencer' || user.type === 'ugc_creator') && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                إحصائيات الحساب
              </h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">4.9</div>
                  <div className="text-sm text-gray-600">التقييم</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">23</div>
                  <div className="text-sm text-gray-600">حملة مكتملة</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">98%</div>
                  <div className="text-sm text-gray-600">معدل الاستجابة</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">96%</div>
                  <div className="text-sm text-gray-600">معدل الإنجاز</div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </div>
    </MobileLayout>
  )
}
