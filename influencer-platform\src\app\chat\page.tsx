'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  ArrowLeft, 
  Send, 
  MessageCircle,
  Bot,
  User,
  Clock,
  CheckCircle2
} from 'lucide-react'

interface Message {
  id: string
  text: string
  sender: 'user' | 'support'
  timestamp: Date
  status?: 'sending' | 'sent' | 'read'
}

export default function ChatPage() {
  const router = useRouter()
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'مرحباً! أنا مساعد الدعم الفني. كيف يمكنني مساعدتك اليوم؟',
      sender: 'support',
      timestamp: new Date(),
      status: 'read'
    }
  ])
  const [newMessage, setNewMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const autoResponses = [
    'شكراً لتواصلك معنا. سأقوم بتوجيه استفسارك إلى الفريق المختص.',
    'يمكنك العثور على معظم الإجابات في مركز المساعدة. هل تريد أن أوجهك إليه؟',
    'سأحتاج إلى بعض التفاصيل الإضافية لمساعدتك بشكل أفضل.',
    'تم تسجيل طلبك برقم #' + Math.floor(Math.random() * 10000) + '. سيتم الرد عليك خلال 24 ساعة.',
    'هل يمكنك توضيح المشكلة أكثر؟ سيساعدني ذلك في تقديم حل أفضل.',
  ]

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      text: newMessage,
      sender: 'user',
      timestamp: new Date(),
      status: 'sending'
    }

    setMessages(prev => [...prev, userMessage])
    setNewMessage('')
    setIsTyping(true)

    // Simulate message sent
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === userMessage.id 
            ? { ...msg, status: 'sent' }
            : msg
        )
      )
    }, 1000)

    // Simulate support response
    setTimeout(() => {
      setIsTyping(false)
      const supportMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: autoResponses[Math.floor(Math.random() * autoResponses.length)],
        sender: 'support',
        timestamp: new Date(),
        status: 'read'
      }
      setMessages(prev => [...prev, supportMessage])
    }, 2000 + Math.random() * 2000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  return (
    <MobileLayout>
      <div className="min-h-screen bg-slate-900 flex flex-col">
        <div className="max-w-4xl mx-auto w-full flex flex-col h-screen">
          {/* Header */}
          <div className="flex items-center p-4 bg-slate-800 border-b border-slate-700">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-slate-700 rounded-lg transition-colors mr-3"
            >
              <ArrowLeft className="w-5 h-5 text-slate-400" />
            </button>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <MessageCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-white">الدعم الفني</h1>
                <p className="text-sm text-green-400">متاح الآن</p>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-xs lg:max-w-md ${message.sender === 'user' ? 'order-2' : 'order-1'}`}>
                  <div
                    className={`rounded-2xl px-4 py-2 ${
                      message.sender === 'user'
                        ? 'bg-green-600 text-white'
                        : 'bg-slate-700 text-slate-100'
                    }`}
                  >
                    <p className="text-sm">{message.text}</p>
                  </div>
                  <div className={`flex items-center mt-1 space-x-1 rtl:space-x-reverse text-xs text-slate-400 ${
                    message.sender === 'user' ? 'justify-end' : 'justify-start'
                  }`}>
                    <Clock className="w-3 h-3" />
                    <span>{formatTime(message.timestamp)}</span>
                    {message.sender === 'user' && message.status === 'sent' && (
                      <CheckCircle2 className="w-3 h-3 text-green-400" />
                    )}
                  </div>
                </div>
                
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.sender === 'user' ? 'order-1 ml-2 bg-blue-600' : 'order-2 mr-2 bg-green-600'
                }`}>
                  {message.sender === 'user' ? (
                    <User className="w-4 h-4 text-white" />
                  ) : (
                    <Bot className="w-4 h-4 text-white" />
                  )}
                </div>
              </motion.div>
            ))}

            {isTyping && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-start"
              >
                <div className="w-8 h-8 rounded-full flex items-center justify-center bg-green-600 mr-2">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div className="bg-slate-700 rounded-2xl px-4 py-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </motion.div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 bg-slate-800 border-t border-slate-700">
            <div className="flex items-end space-x-2 rtl:space-x-reverse">
              <div className="flex-1">
                <textarea
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="اكتب رسالتك هنا..."
                  className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 resize-none focus:outline-none focus:border-green-500"
                  rows={1}
                  style={{ minHeight: '44px', maxHeight: '120px' }}
                />
              </div>
              <Button
                onClick={handleSendMessage}
                disabled={!newMessage.trim()}
                variant="primary"
                size="sm"
                className="px-4 py-3"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="p-4 bg-slate-800">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setNewMessage('أحتاج مساعدة في التسجيل')}
                className="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-xs hover:bg-slate-600 transition-colors"
              >
                مساعدة في التسجيل
              </button>
              <button
                onClick={() => setNewMessage('مشكلة في الدفع')}
                className="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-xs hover:bg-slate-600 transition-colors"
              >
                مشكلة في الدفع
              </button>
              <button
                onClick={() => setNewMessage('كيف أستخدم المنصة؟')}
                className="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-xs hover:bg-slate-600 transition-colors"
              >
                كيفية الاستخدام
              </button>
              <button
                onClick={() => router.push('/help')}
                className="px-3 py-1 bg-blue-600 text-white rounded-full text-xs hover:bg-blue-700 transition-colors"
              >
                مركز المساعدة
              </button>
            </div>
          </div>
        </div>
      </div>
    </MobileLayout>
  )
}
