'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import {
  ArrowRight,
  ArrowLeft,
  Upload,
  X,
  Calendar,
  DollarSign,
  Target,
  FileText
} from 'lucide-react'
import { getServiceTypes, getSaudiCities, getInterests } from '@/lib/utils'

export default function CreateCampaignPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [step, setStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)

  const [formData, setFormData] = useState({
    // Basic Info
    title: '',
    description: '',
    serviceType: '',
    budget: '',
    deadline: '',

    // Requirements
    requirements: [''],
    deliverables: [''],

    // Product Info
    productImages: [] as File[],
    storeLink: '',

    // Target Audience
    targetAudience: {
      ageRange: '',
      gender: '',
      interests: [] as string[],
      location: ''
    }
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }

    if (user?.type !== 'merchant') {
      router.push('/')
      return
    }
  }, [isAuthenticated, user, router])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleArrayChange = (field: 'requirements' | 'deliverables', index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }))
  }

  const addArrayItem = (field: 'requirements' | 'deliverables') => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }))
  }

  const removeArrayItem = (field: 'requirements' | 'deliverables', index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }))
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setFormData(prev => ({
      ...prev,
      productImages: [...prev.productImages, ...files].slice(0, 5) // Max 5 images
    }))
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      productImages: prev.productImages.filter((_, i) => i !== index)
    }))
  }

  const handleTargetAudienceChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      targetAudience: {
        ...prev.targetAudience,
        [field]: value
      }
    }))
  }

  const validateStep1 = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان الحملة مطلوب'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف الحملة مطلوب'
    }

    if (!formData.serviceType) {
      newErrors.serviceType = 'نوع الخدمة مطلوب'
    }

    if (!formData.budget) {
      newErrors.budget = 'الميزانية مطلوبة'
    } else if (isNaN(Number(formData.budget)) || Number(formData.budget) <= 0) {
      newErrors.budget = 'الميزانية يجب أن تكون رقم صحيح'
    }

    if (!formData.deadline) {
      newErrors.deadline = 'تاريخ التسليم مطلوب'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validateStep2 = () => {
    const newErrors: Record<string, string> = {}

    const validRequirements = formData.requirements.filter(req => req.trim())
    if (validRequirements.length === 0) {
      newErrors.requirements = 'يجب إضافة متطلب واحد على الأقل'
    }

    const validDeliverables = formData.deliverables.filter(del => del.trim())
    if (validDeliverables.length === 0) {
      newErrors.deliverables = 'يجب إضافة مخرج واحد على الأقل'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validateStep3 = () => {
    const newErrors: Record<string, string> = {}

    if (formData.productImages.length === 0) {
      newErrors.productImages = 'يجب رفع صورة واحدة على الأقل للمنتج'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    let isValid = false

    switch (step) {
      case 1:
        isValid = validateStep1()
        break
      case 2:
        isValid = validateStep2()
        break
      case 3:
        isValid = validateStep3()
        break
      default:
        isValid = true
    }

    if (isValid && step < 4) {
      setStep(step + 1)
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const handleSubmit = async () => {
    if (!validateStep3()) return

    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // In real app, this would create the campaign via API
      console.log('Campaign data:', formData)

      // Redirect to campaigns list
      router.push('/merchant/campaigns')
    } catch (error) {
      setErrors({ general: 'حدث خطأ أثناء إنشاء الحملة. يرجى المحاولة مرة أخرى.' })
    } finally {
      setIsLoading(false)
    }
  }

  const serviceTypes = getServiceTypes()
  const cities = getSaudiCities()
  const interests = getInterests()

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            إنشاء حملة جديدة
          </h1>
          <p className="text-gray-600">
            أنشئ حملة إعلانية احترافية مع المؤثرين
          </p>
        </div>

        {/* Progress Indicator */}
        <Card>
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              {[1, 2, 3, 4].map((stepNumber) => (
                <React.Fragment key={stepNumber}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step >= stepNumber ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'
                  }`}>
                    {stepNumber}
                  </div>
                  {stepNumber < 4 && (
                    <div className={`w-8 h-1 ${step > stepNumber ? 'bg-green-500' : 'bg-gray-200'}`} />
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>

          <div className="text-center text-sm text-gray-600">
            {step === 1 && 'المعلومات الأساسية'}
            {step === 2 && 'المتطلبات والمخرجات'}
            {step === 3 && 'صور المنتج والروابط'}
            {step === 4 && 'الجمهور المستهدف'}
          </div>
        </Card>

        {errors.general && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 text-sm">{errors.general}</p>
          </div>
        )}

        {/* Step 1: Basic Information */}
        {step === 1 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <Card>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                المعلومات الأساسية
              </h2>

              <div className="space-y-4">
                <Input
                  label="عنوان الحملة"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  error={errors.title}
                  placeholder="مثال: إعلان منتج العناية بالبشرة"
                />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    وصف الحملة
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className={`input-field min-h-[100px] resize-none ${errors.description ? 'border-red-500' : ''}`}
                    placeholder="اكتب وصفاً مفصلاً عن الحملة والهدف منها..."
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الخدمة
                  </label>
                  <select
                    value={formData.serviceType}
                    onChange={(e) => handleInputChange('serviceType', e.target.value)}
                    className={`input-field ${errors.serviceType ? 'border-red-500' : ''}`}
                  >
                    <option value="">اختر نوع الخدمة</option>
                    {serviceTypes.map(service => (
                      <option key={service.id} value={service.id}>
                        {service.name} - {service.description}
                      </option>
                    ))}
                  </select>
                  {errors.serviceType && (
                    <p className="mt-1 text-sm text-red-600">{errors.serviceType}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="الميزانية (ريال)"
                    type="number"
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', e.target.value)}
                    error={errors.budget}
                    leftIcon={<DollarSign className="w-5 h-5" />}
                    placeholder="2500"
                  />

                  <Input
                    label="تاريخ التسليم"
                    type="date"
                    value={formData.deadline}
                    onChange={(e) => handleInputChange('deadline', e.target.value)}
                    error={errors.deadline}
                    leftIcon={<Calendar className="w-5 h-5" />}
                  />
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Step 2: Requirements and Deliverables */}
        {step === 2 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <Card>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Target className="w-5 h-5 mr-2" />
                المتطلبات والمخرجات
              </h2>

              <div className="space-y-6">
                {/* Requirements */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    متطلبات الحملة
                  </label>
                  <div className="space-y-2">
                    {formData.requirements.map((requirement, index) => (
                      <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Input
                          value={requirement}
                          onChange={(e) => handleArrayChange('requirements', index, e.target.value)}
                          placeholder={`متطلب ${index + 1}`}
                          className="flex-1"
                        />
                        {formData.requirements.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeArrayItem('requirements', index)}
                            className="p-2 text-red-500 hover:text-red-700"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => addArrayItem('requirements')}
                      className="text-green-600 hover:text-green-700 text-sm font-medium"
                    >
                      + إضافة متطلب
                    </button>
                  </div>
                  {errors.requirements && (
                    <p className="mt-1 text-sm text-red-600">{errors.requirements}</p>
                  )}
                </div>

                {/* Deliverables */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    المخرجات المطلوبة
                  </label>
                  <div className="space-y-2">
                    {formData.deliverables.map((deliverable, index) => (
                      <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Input
                          value={deliverable}
                          onChange={(e) => handleArrayChange('deliverables', index, e.target.value)}
                          placeholder={`مخرج ${index + 1}`}
                          className="flex-1"
                        />
                        {formData.deliverables.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeArrayItem('deliverables', index)}
                            className="p-2 text-red-500 hover:text-red-700"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => addArrayItem('deliverables')}
                      className="text-green-600 hover:text-green-700 text-sm font-medium"
                    >
                      + إضافة مخرج
                    </button>
                  </div>
                  {errors.deliverables && (
                    <p className="mt-1 text-sm text-red-600">{errors.deliverables}</p>
                  )}
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Step 3: Product Images and Links */}
        {step === 3 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <Card>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Upload className="w-5 h-5 mr-2" />
                صور المنتج والروابط
              </h2>

              <div className="space-y-6">
                {/* Product Images */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    صور المنتج (حد أقصى 5 صور)
                  </label>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    {formData.productImages.map((image, index) => (
                      <div key={index} className="relative">
                        <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`Product ${index + 1}`}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        </div>
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}

                    {formData.productImages.length < 5 && (
                      <label className="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-green-500 transition-colors">
                        <Upload className="w-8 h-8 text-gray-400 mb-2" />
                        <span className="text-sm text-gray-600">رفع صورة</span>
                        <input
                          type="file"
                          accept="image/*"
                          multiple
                          onChange={handleImageUpload}
                          className="hidden"
                        />
                      </label>
                    )}
                  </div>

                  {errors.productImages && (
                    <p className="mt-1 text-sm text-red-600">{errors.productImages}</p>
                  )}
                </div>

                {/* Store Link */}
                <Input
                  label="رابط المتجر (اختياري)"
                  value={formData.storeLink}
                  onChange={(e) => handleInputChange('storeLink', e.target.value)}
                  placeholder="https://example.com"
                  helperText="رابط موقعك أو متجرك الإلكتروني"
                />
              </div>
            </Card>
          </motion.div>
        )}

        {/* Step 4: Target Audience */}
        {step === 4 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <Card>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Target className="w-5 h-5 mr-2" />
                الجمهور المستهدف
              </h2>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الفئة العمرية
                    </label>
                    <select
                      value={formData.targetAudience.ageRange}
                      onChange={(e) => handleTargetAudienceChange('ageRange', e.target.value)}
                      className="input-field"
                    >
                      <option value="">اختر الفئة العمرية</option>
                      <option value="13-17">13-17 سنة</option>
                      <option value="18-24">18-24 سنة</option>
                      <option value="25-34">25-34 سنة</option>
                      <option value="35-44">35-44 سنة</option>
                      <option value="45-54">45-54 سنة</option>
                      <option value="55+">55+ سنة</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الجنس
                    </label>
                    <select
                      value={formData.targetAudience.gender}
                      onChange={(e) => handleTargetAudienceChange('gender', e.target.value)}
                      className="input-field"
                    >
                      <option value="">اختر الجنس</option>
                      <option value="male">ذكور</option>
                      <option value="female">إناث</option>
                      <option value="both">كلاهما</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    المدينة المستهدفة
                  </label>
                  <select
                    value={formData.targetAudience.location}
                    onChange={(e) => handleTargetAudienceChange('location', e.target.value)}
                    className="input-field"
                  >
                    <option value="">اختر المدينة</option>
                    {cities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الاهتمامات
                  </label>
                  <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                    {interests.map(interest => (
                      <label key={interest} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.targetAudience.interests.includes(interest)}
                          onChange={(e) => {
                            const newInterests = e.target.checked
                              ? [...formData.targetAudience.interests, interest]
                              : formData.targetAudience.interests.filter(i => i !== interest)
                            handleTargetAudienceChange('interests', newInterests)
                          }}
                          className="rounded border-gray-300 text-green-600 focus:ring-green-500 mr-2"
                        />
                        <span className="text-sm text-gray-700">{interest}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Navigation Buttons */}
        <div className="flex space-x-3 rtl:space-x-reverse">
          {step > 1 && (
            <Button
              variant="outline"
              onClick={handleBack}
              leftIcon={<ArrowLeft className="w-5 h-5" />}
              className="flex-1"
            >
              السابق
            </Button>
          )}

          {step < 4 ? (
            <Button
              variant="primary"
              onClick={handleNext}
              rightIcon={<ArrowRight className="w-5 h-5" />}
              className="flex-1"
            >
              التالي
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={handleSubmit}
              isLoading={isLoading}
              rightIcon={<ArrowRight className="w-5 h-5" />}
              className="flex-1"
            >
              إنشاء الحملة
            </Button>
          )}
        </div>
      </div>
    </MobileLayout>
  )
}
