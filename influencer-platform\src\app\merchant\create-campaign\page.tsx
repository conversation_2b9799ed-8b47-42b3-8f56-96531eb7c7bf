'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/store'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  ArrowRight, 
  ArrowLeft, 
  Upload, 
  X, 
  Calendar,
  DollarSign,
  Target,
  FileText
} from 'lucide-react'
import { getServiceTypes, getSaudiCities, getInterests } from '@/lib/utils'

export default function CreateCampaignPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [step, setStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  
  const [formData, setFormData] = useState({
    // Basic Info
    title: '',
    description: '',
    serviceType: '',
    budget: '',
    deadline: '',
    
    // Requirements
    requirements: [''],
    deliverables: [''],
    
    // Product Info
    productImages: [] as File[],
    storeLink: '',
    
    // Target Audience
    targetAudience: {
      ageRange: '',
      gender: '',
      interests: [] as string[],
      location: ''
    }
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }
    
    if (user?.type !== 'merchant') {
      router.push('/')
      return
    }
  }, [isAuthenticated, user, router])
  
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }
  
  const handleArrayChange = (field: 'requirements' | 'deliverables', index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }))
  }
  
  const addArrayItem = (field: 'requirements' | 'deliverables') => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }))
  }
  
  const removeArrayItem = (field: 'requirements' | 'deliverables', index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }))
  }
  
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setFormData(prev => ({
      ...prev,
      productImages: [...prev.productImages, ...files].slice(0, 5) // Max 5 images
    }))
  }
  
  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      productImages: prev.productImages.filter((_, i) => i !== index)
    }))
  }
  
  const handleTargetAudienceChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      targetAudience: {
        ...prev.targetAudience,
        [field]: value
      }
    }))
  }
  
  const validateStep1 = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.title.trim()) {
      newErrors.title = 'عنوان الحملة مطلوب'
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'وصف الحملة مطلوب'
    }
    
    if (!formData.serviceType) {
      newErrors.serviceType = 'نوع الخدمة مطلوب'
    }
    
    if (!formData.budget) {
      newErrors.budget = 'الميزانية مطلوبة'
    } else if (isNaN(Number(formData.budget)) || Number(formData.budget) <= 0) {
      newErrors.budget = 'الميزانية يجب أن تكون رقم صحيح'
    }
    
    if (!formData.deadline) {
      newErrors.deadline = 'تاريخ التسليم مطلوب'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const validateStep2 = () => {
    const newErrors: Record<string, string> = {}
    
    const validRequirements = formData.requirements.filter(req => req.trim())
    if (validRequirements.length === 0) {
      newErrors.requirements = 'يجب إضافة متطلب واحد على الأقل'
    }
    
    const validDeliverables = formData.deliverables.filter(del => del.trim())
    if (validDeliverables.length === 0) {
      newErrors.deliverables = 'يجب إضافة مخرج واحد على الأقل'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const validateStep3 = () => {
    const newErrors: Record<string, string> = {}
    
    if (formData.productImages.length === 0) {
      newErrors.productImages = 'يجب رفع صورة واحدة على الأقل للمنتج'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const handleNext = () => {
    let isValid = false
    
    switch (step) {
      case 1:
        isValid = validateStep1()
        break
      case 2:
        isValid = validateStep2()
        break
      case 3:
        isValid = validateStep3()
        break
      default:
        isValid = true
    }
    
    if (isValid && step < 4) {
      setStep(step + 1)
    }
  }
  
  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }
  
  const handleSubmit = async () => {
    if (!validateStep3()) return
    
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // In real app, this would create the campaign via API
      console.log('Campaign data:', formData)
      
      // Redirect to campaigns list
      router.push('/merchant/campaigns')
    } catch (error) {
      setErrors({ general: 'حدث خطأ أثناء إنشاء الحملة. يرجى المحاولة مرة أخرى.' })
    } finally {
      setIsLoading(false)
    }
  }
  
  const serviceTypes = getServiceTypes()
  const cities = getSaudiCities()
  const interests = getInterests()
  
  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            إنشاء حملة جديدة
          </h1>
          <p className="text-gray-600">
            أنشئ حملة إعلانية احترافية مع المؤثرين
          </p>
        </div>
        
        {/* Progress Indicator */}
        <Card>
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              {[1, 2, 3, 4].map((stepNumber) => (
                <React.Fragment key={stepNumber}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step >= stepNumber ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'
                  }`}>
                    {stepNumber}
                  </div>
                  {stepNumber < 4 && (
                    <div className={`w-8 h-1 ${step > stepNumber ? 'bg-green-500' : 'bg-gray-200'}`} />
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>
          
          <div className="text-center text-sm text-gray-600">
            {step === 1 && 'المعلومات الأساسية'}
            {step === 2 && 'المتطلبات والمخرجات'}
            {step === 3 && 'صور المنتج والروابط'}
            {step === 4 && 'الجمهور المستهدف'}
          </div>
        </Card>
        
        {errors.general && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 text-sm">{errors.general}</p>
          </div>
        )}
        
        {/* Step 1: Basic Information */}
        {step === 1 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <Card>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                المعلومات الأساسية
              </h2>
              
              <div className="space-y-4">
                <Input
                  label="عنوان الحملة"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  error={errors.title}
                  placeholder="مثال: إعلان منتج العناية بالبشرة"
                />
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    وصف الحملة
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className={`input-field min-h-[100px] resize-none ${errors.description ? 'border-red-500' : ''}`}
                    placeholder="اكتب وصفاً مفصلاً عن الحملة والهدف منها..."
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الخدمة
                  </label>
                  <select
                    value={formData.serviceType}
                    onChange={(e) => handleInputChange('serviceType', e.target.value)}
                    className={`input-field ${errors.serviceType ? 'border-red-500' : ''}`}
                  >
                    <option value="">اختر نوع الخدمة</option>
                    {serviceTypes.map(service => (
                      <option key={service.id} value={service.id}>
                        {service.name} - {service.description}
                      </option>
                    ))}
                  </select>
                  {errors.serviceType && (
                    <p className="mt-1 text-sm text-red-600">{errors.serviceType}</p>
                  )}
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="الميزانية (ريال)"
                    type="number"
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', e.target.value)}
                    error={errors.budget}
                    leftIcon={<DollarSign className="w-5 h-5" />}
                    placeholder="2500"
                  />
                  
                  <Input
                    label="تاريخ التسليم"
                    type="date"
                    value={formData.deadline}
                    onChange={(e) => handleInputChange('deadline', e.target.value)}
                    error={errors.deadline}
                    leftIcon={<Calendar className="w-5 h-5" />}
                  />
                </div>
              </div>
            </Card>
          </motion.div>
        )}
