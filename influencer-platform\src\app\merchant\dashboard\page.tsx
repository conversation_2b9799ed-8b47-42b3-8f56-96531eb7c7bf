'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '@/lib/store'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { 
  TrendingUp, 
  Users, 
  Clock, 
  CheckCircle, 
  Plus,
  Eye,
  MessageCircle,
  Star,
  Calendar,
  DollarSign
} from 'lucide-react'
import { formatPrice, formatDate, getStatusColor, getStatusText } from '@/lib/utils'

export default function MerchantDashboard() {
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  
  // Mock data - in real app, this would come from API
  const [stats, setStats] = useState({
    totalCampaigns: 12,
    activeCampaigns: 3,
    completedCampaigns: 8,
    totalSpent: 15000,
    averageRating: 4.8,
    responseRate: 95
  })
  
  const [recentCampaigns, setRecentCampaigns] = useState([
    {
      id: '1',
      title: 'إعلان منتج العناية بالبشرة',
      influencerName: 'سارة أحمد',
      influencerAvatar: '/avatars/sarah.jpg',
      status: 'in_progress',
      budget: 2500,
      deadline: new Date('2024-02-15'),
      platform: 'instagram',
      serviceType: 'instagram_reel'
    },
    {
      id: '2',
      title: 'حملة إطلاق المجموعة الجديدة',
      influencerName: 'محمد علي',
      influencerAvatar: '/avatars/mohammed.jpg',
      status: 'completed',
      budget: 5000,
      deadline: new Date('2024-01-20'),
      platform: 'snapchat',
      serviceType: 'full_story'
    },
    {
      id: '3',
      title: 'تجربة منتج في المحل',
      influencerName: 'نورا خالد',
      influencerAvatar: '/avatars/nora.jpg',
      status: 'pending',
      budget: 1500,
      deadline: new Date('2024-02-10'),
      platform: 'tiktok',
      serviceType: 'store_visit'
    }
  ])
  
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }
    
    if (user?.type !== 'merchant') {
      router.push('/')
      return
    }
    
    // Simulate loading
    setTimeout(() => setIsLoading(false), 1000)
  }, [isAuthenticated, user, router])
  
  if (isLoading) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }
  
  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            مرحباً، {user?.name}
          </h1>
          <p className="text-gray-600">
            إليك نظرة عامة على حملاتك الإعلانية
          </p>
        </div>
        
        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stats.totalCampaigns}
              </div>
              <div className="text-sm text-gray-600">
                إجمالي الحملات
              </div>
            </Card>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stats.activeCampaigns}
              </div>
              <div className="text-sm text-gray-600">
                حملات نشطة
              </div>
            </Card>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <DollarSign className="w-6 h-6 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {formatPrice(stats.totalSpent)}
              </div>
              <div className="text-sm text-gray-600">
                إجمالي الإنفاق
              </div>
            </Card>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stats.averageRating}
              </div>
              <div className="text-sm text-gray-600">
                متوسط التقييم
              </div>
            </Card>
          </motion.div>
        </div>
        
        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              إجراءات سريعة
            </h2>
            
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="primary"
                size="sm"
                leftIcon={<Plus className="w-4 h-4" />}
                onClick={() => router.push('/merchant/create-campaign')}
                className="text-sm"
              >
                حملة جديدة
              </Button>
              
              <Button
                variant="secondary"
                size="sm"
                leftIcon={<Users className="w-4 h-4" />}
                onClick={() => router.push('/search')}
                className="text-sm"
              >
                تصفح المؤثرين
              </Button>
            </div>
          </Card>
        </motion.div>
        
        {/* Recent Campaigns */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                الحملات الأخيرة
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/merchant/campaigns')}
              >
                عرض الكل
              </Button>
            </div>
            
            <div className="space-y-4">
              {recentCampaigns.map((campaign, index) => (
                <motion.div
                  key={campaign.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 + index * 0.1 }}
                  className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onClick={() => router.push(`/merchant/campaigns/${campaign.id}`)}
                >
                  <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center text-white font-bold">
                    {campaign.influencerName.charAt(0)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900 truncate">
                      {campaign.title}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {campaign.influencerName} • {formatPrice(campaign.budget)}
                    </p>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                      <Badge
                        variant={
                          campaign.status === 'completed' ? 'success' :
                          campaign.status === 'in_progress' ? 'info' :
                          campaign.status === 'pending' ? 'warning' : 'default'
                        }
                        size="sm"
                      >
                        {getStatusText(campaign.status)}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {formatDate(campaign.deadline)}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <button className="p-2 text-gray-400 hover:text-gray-600">
                      <MessageCircle className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600">
                      <Eye className="w-4 h-4" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </Card>
        </motion.div>
        
        {/* Performance Insights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              إحصائيات الأداء
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">معدل الاستجابة</span>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-20 h-2 bg-gray-200 rounded-full">
                    <div 
                      className="h-full bg-green-500 rounded-full"
                      style={{ width: `${stats.responseRate}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {stats.responseRate}%
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">الحملات المكتملة</span>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-20 h-2 bg-gray-200 rounded-full">
                    <div 
                      className="h-full bg-blue-500 rounded-full"
                      style={{ width: `${(stats.completedCampaigns / stats.totalCampaigns) * 100}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {Math.round((stats.completedCampaigns / stats.totalCampaigns) * 100)}%
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
