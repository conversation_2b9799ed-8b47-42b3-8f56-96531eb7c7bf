'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { 
  ArrowLeft,
  Plus,
  Edit3,
  Trash2,
  Eye,
  Camera,
  Video,
  Image,
  ExternalLink,
  Heart,
  MessageCircle,
  Share,
  Upload
} from 'lucide-react'
import { formatNumber } from '@/lib/utils'

interface PortfolioItem {
  id: string
  type: 'image' | 'video' | 'reel'
  title: string
  description: string
  platform: 'instagram' | 'snapchat' | 'tiktok' | 'youtube'
  url: string
  thumbnail: string
  stats: {
    views: number
    likes: number
    comments: number
    shares?: number
  }
  createdAt: string
  campaign?: {
    brand: string
    category: string
  }
}

export default function PortfolioPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showAddForm, setShowAddForm] = useState(false)

  // Mock portfolio data
  const [portfolioItems] = useState<PortfolioItem[]>([
    {
      id: '1',
      type: 'reel',
      title: 'مراجعة منتج العناية بالبشرة',
      description: 'ريل عن تجربتي مع كريم الوجه الجديد من براند معروف',
      platform: 'instagram',
      url: 'https://instagram.com/reel/abc123',
      thumbnail: '/portfolio/reel1.jpg',
      stats: {
        views: 45000,
        likes: 3200,
        comments: 180,
        shares: 95
      },
      createdAt: '2024-01-15',
      campaign: {
        brand: 'براند الجمال',
        category: 'beauty'
      }
    },
    {
      id: '2',
      type: 'video',
      title: 'جولة في مطعم جديد',
      description: 'فيديو تيك توك عن تجربة الطعام في مطعم جديد بالرياض',
      platform: 'tiktok',
      url: 'https://tiktok.com/@user/video/xyz789',
      thumbnail: '/portfolio/tiktok1.jpg',
      stats: {
        views: 120000,
        likes: 8500,
        comments: 420,
        shares: 230
      },
      createdAt: '2024-01-10',
      campaign: {
        brand: 'مطعم الذواقة',
        category: 'food'
      }
    },
    {
      id: '3',
      type: 'image',
      title: 'إطلالة عصرية',
      description: 'صورة إنستغرام لإطلالة كاجوال مع ملابس من براند محلي',
      platform: 'instagram',
      url: 'https://instagram.com/p/def456',
      thumbnail: '/portfolio/post1.jpg',
      stats: {
        views: 25000,
        likes: 2100,
        comments: 95
      },
      createdAt: '2024-01-08',
      campaign: {
        brand: 'براند الأزياء',
        category: 'fashion'
      }
    }
  ])

  const [filteredItems, setFilteredItems] = useState(portfolioItems)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, router])

  useEffect(() => {
    let filtered = portfolioItems

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => {
        if (selectedCategory === 'campaigns') {
          return item.campaign
        }
        return item.platform === selectedCategory
      })
    }

    setFilteredItems(filtered)
  }, [selectedCategory, portfolioItems])

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'instagram': return '📷'
      case 'tiktok': return '🎵'
      case 'snapchat': return '👻'
      case 'youtube': return '📺'
      default: return '📱'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return Video
      case 'reel': return Video
      case 'image': return Image
      default: return Camera
    }
  }

  const handleDelete = (itemId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا العمل؟')) {
      // Handle delete
      alert('تم حذف العمل بنجاح')
    }
  }

  if (!user) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">معرض الأعمال</h1>
              <p className="text-gray-600">{filteredItems.length} عمل</p>
            </div>
          </div>
          
          <Button
            variant="primary"
            size="sm"
            onClick={() => setShowAddForm(true)}
            leftIcon={<Plus className="w-4 h-4" />}
          >
            إضافة
          </Button>
        </div>

        {/* Stats Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <h3 className="font-semibold text-gray-900 mb-4">إحصائيات المعرض</h3>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {formatNumber(portfolioItems.reduce((sum, item) => sum + item.stats.views, 0))}
                </div>
                <div className="text-sm text-gray-600">إجمالي المشاهدات</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {formatNumber(portfolioItems.reduce((sum, item) => sum + item.stats.likes, 0))}
                </div>
                <div className="text-sm text-gray-600">إجمالي الإعجابات</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {portfolioItems.filter(item => item.campaign).length}
                </div>
                <div className="text-sm text-gray-600">حملات مدفوعة</div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Category Filter */}
        <div className="flex space-x-2 rtl:space-x-reverse overflow-x-auto pb-2">
          {[
            { key: 'all', label: 'الكل' },
            { key: 'campaigns', label: 'الحملات' },
            { key: 'instagram', label: 'إنستغرام' },
            { key: 'tiktok', label: 'تيك توك' },
            { key: 'snapchat', label: 'سناب شات' }
          ].map((category) => (
            <button
              key={category.key}
              onClick={() => setSelectedCategory(category.key)}
              className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all ${
                selectedCategory === category.key
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>

        {/* Portfolio Grid */}
        <div className="space-y-4">
          {filteredItems.map((item, index) => {
            const TypeIcon = getTypeIcon(item.type)
            return (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
              >
                <Card>
                  <div className="space-y-4">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 rtl:space-x-reverse flex-1">
                        <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center relative overflow-hidden">
                          <TypeIcon className="w-6 h-6 text-gray-500" />
                          <div className="absolute top-1 right-1 text-lg">
                            {getPlatformIcon(item.platform)}
                          </div>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                            <h3 className="font-semibold text-gray-900 truncate">
                              {item.title}
                            </h3>
                            {item.campaign && (
                              <Badge variant="success" size="sm">
                                حملة مدفوعة
                              </Badge>
                            )}
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                            {item.description}
                          </p>
                          
                          {item.campaign && (
                            <div className="text-xs text-gray-500">
                              {item.campaign.brand} • {item.campaign.category}
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(item.url, '_blank')}
                        >
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(item.id)}
                          className="text-red-600 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-600">
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <Eye className="w-4 h-4" />
                          <span>{formatNumber(item.stats.views)}</span>
                        </div>
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <Heart className="w-4 h-4" />
                          <span>{formatNumber(item.stats.likes)}</span>
                        </div>
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <MessageCircle className="w-4 h-4" />
                          <span>{formatNumber(item.stats.comments)}</span>
                        </div>
                        {item.stats.shares && (
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <Share className="w-4 h-4" />
                            <span>{formatNumber(item.stats.shares)}</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="text-xs text-gray-500">
                        {new Date(item.createdAt).toLocaleDateString('ar-SA')}
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Empty State */}
        {filteredItems.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="text-gray-400 text-6xl mb-4">📸</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              لا توجد أعمال
            </h3>
            <p className="text-gray-600 mb-4">
              ابدأ بإضافة أعمالك لبناء معرض احترافي
            </p>
            <Button
              variant="primary"
              onClick={() => setShowAddForm(true)}
              leftIcon={<Plus className="w-5 h-5" />}
            >
              إضافة عمل جديد
            </Button>
          </motion.div>
        )}

        {/* Add Form Modal */}
        {showAddForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-black/50 flex items-end z-50"
            onClick={() => setShowAddForm(false)}
          >
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              className="w-full bg-white rounded-t-2xl p-6 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">
                  إضافة عمل جديد
                </h2>
                <button
                  onClick={() => setShowAddForm(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="text-center p-8 border-2 border-dashed border-gray-300 rounded-lg">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    ارفع ملفك
                  </h3>
                  <p className="text-gray-600 mb-4">
                    اسحب وأفلت الملف هنا أو اضغط للاختيار
                  </p>
                  <Button variant="outline">
                    اختيار ملف
                  </Button>
                </div>

                <div className="text-center">
                  <p className="text-sm text-gray-500">
                    أو أضف رابط من منصات التواصل
                  </p>
                  <Button
                    variant="ghost"
                    className="mt-2"
                    onClick={() => alert('ميزة إضافة الروابط قريباً!')}
                  >
                    إضافة رابط
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Tips */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="bg-purple-50 border-purple-200">
            <div className="flex items-start space-x-3 rtl:space-x-reverse">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <Camera className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <h3 className="font-semibold text-purple-900 mb-1">
                  نصائح لمعرض أفضل
                </h3>
                <div className="text-sm text-purple-800 space-y-1">
                  <div>• أضف أفضل أعمالك وأكثرها تفاعلاً</div>
                  <div>• اكتب وصف واضح لكل عمل</div>
                  <div>• أظهر تنوع المحتوى والمنصات</div>
                  <div>• حدث المعرض بانتظام</div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
