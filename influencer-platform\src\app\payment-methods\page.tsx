'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import { 
  ArrowLeft,
  CreditCard,
  Plus,
  Trash2,
  Edit3,
  Check,
  X,
  Shield,
  Smartphone,
  Building,
  AlertCircle
} from 'lucide-react'

interface PaymentMethod {
  id: string
  type: 'card' | 'bank' | 'wallet'
  name: string
  details: string
  isDefault: boolean
  lastUsed?: string
}

export default function PaymentMethodsPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: '1',
      type: 'card',
      name: 'بطاقة الراجحي',
      details: '**** **** **** 1234',
      isDefault: true,
      lastUsed: '2024-01-15'
    },
    {
      id: '2',
      type: 'wallet',
      name: 'Apple Pay',
      details: 'iPhone 15 Pro',
      isDefault: false,
      lastUsed: '2024-01-10'
    }
  ])
  
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    type: 'card' as 'card' | 'bank' | 'wallet',
    name: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    holderName: '',
    bankName: '',
    iban: ''
  })

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, router])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleAddPaymentMethod = () => {
    const newMethod: PaymentMethod = {
      id: Date.now().toString(),
      type: formData.type,
      name: formData.name,
      details: formData.type === 'card' 
        ? `**** **** **** ${formData.cardNumber.slice(-4)}`
        : formData.type === 'bank'
        ? `IBAN: ${formData.iban.slice(-4)}`
        : formData.name,
      isDefault: paymentMethods.length === 0
    }
    
    setPaymentMethods(prev => [...prev, newMethod])
    setShowAddForm(false)
    setFormData({
      type: 'card',
      name: '',
      cardNumber: '',
      expiryDate: '',
      cvv: '',
      holderName: '',
      bankName: '',
      iban: ''
    })
  }

  const handleSetDefault = (id: string) => {
    setPaymentMethods(prev => 
      prev.map(method => ({
        ...method,
        isDefault: method.id === id
      }))
    )
  }

  const handleDelete = (id: string) => {
    if (confirm('هل أنت متأكد من حذف طريقة الدفع هذه؟')) {
      setPaymentMethods(prev => prev.filter(method => method.id !== id))
    }
  }

  const getPaymentIcon = (type: string) => {
    switch (type) {
      case 'card': return CreditCard
      case 'bank': return Building
      case 'wallet': return Smartphone
      default: return CreditCard
    }
  }

  const getPaymentColor = (type: string) => {
    switch (type) {
      case 'card': return 'bg-blue-100 text-blue-600'
      case 'bank': return 'bg-green-100 text-green-600'
      case 'wallet': return 'bg-purple-100 text-purple-600'
      default: return 'bg-gray-100 text-gray-600'
    }
  }

  if (!user) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">طرق الدفع</h1>
              <p className="text-gray-600">إدارة طرق الدفع والاستلام</p>
            </div>
          </div>
          
          <Button
            variant="primary"
            size="sm"
            onClick={() => setShowAddForm(true)}
            leftIcon={<Plus className="w-4 h-4" />}
          >
            إضافة
          </Button>
        </div>

        {/* Security Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-blue-50 border-blue-200">
            <div className="flex items-start space-x-3 rtl:space-x-reverse">
              <Shield className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-1">
                  أمان معلوماتك
                </h3>
                <p className="text-blue-800 text-sm">
                  جميع معلومات الدفع مشفرة ومحمية بأعلى معايير الأمان. 
                  نحن لا نحتفظ بتفاصيل البطاقات الكاملة على خوادمنا.
                </p>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Payment Methods List */}
        <div className="space-y-4">
          {paymentMethods.map((method, index) => {
            const Icon = getPaymentIcon(method.type)
            return (
              <motion.div
                key={method.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
              >
                <Card>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                      <div className={`w-12 h-12 ${getPaymentColor(method.type)} rounded-xl flex items-center justify-center`}>
                        <Icon className="w-6 h-6" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                          <h3 className="font-semibold text-gray-900">
                            {method.name}
                          </h3>
                          {method.isDefault && (
                            <Badge variant="success" size="sm">
                              افتراضي
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-gray-600 text-sm">
                          {method.details}
                        </p>
                        
                        {method.lastUsed && (
                          <p className="text-gray-500 text-xs mt-1">
                            آخر استخدام: {new Date(method.lastUsed).toLocaleDateString('ar-SA')}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      {!method.isDefault && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSetDefault(method.id)}
                        >
                          تعيين كافتراضي
                        </Button>
                      )}
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(method.id)}
                        className="text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Empty State */}
        {paymentMethods.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12"
          >
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CreditCard className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              لا توجد طرق دفع
            </h3>
            <p className="text-gray-600 mb-6">
              أضف طريقة دفع لتتمكن من استخدام المنصة
            </p>
            <Button
              variant="primary"
              onClick={() => setShowAddForm(true)}
              leftIcon={<Plus className="w-5 h-5" />}
            >
              إضافة طريقة دفع
            </Button>
          </motion.div>
        )}

        {/* Add Payment Method Form */}
        {showAddForm && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="fixed inset-0 bg-black/50 flex items-end z-50"
            onClick={() => setShowAddForm(false)}
          >
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              className="w-full bg-white rounded-t-2xl p-6 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">
                  إضافة طريقة دفع
                </h2>
                <button
                  onClick={() => setShowAddForm(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-gray-600" />
                </button>
              </div>

              <div className="space-y-4">
                {/* Payment Type Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع طريقة الدفع
                  </label>
                  <div className="grid grid-cols-3 gap-2">
                    {[
                      { value: 'card', label: 'بطاقة ائتمان', icon: CreditCard },
                      { value: 'bank', label: 'حساب بنكي', icon: Building },
                      { value: 'wallet', label: 'محفظة رقمية', icon: Smartphone }
                    ].map((type) => {
                      const Icon = type.icon
                      return (
                        <button
                          key={type.value}
                          onClick={() => handleInputChange('type', type.value)}
                          className={`p-3 border rounded-lg text-center transition-colors ${
                            formData.type === type.value
                              ? 'border-green-500 bg-green-50 text-green-700'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <Icon className="w-6 h-6 mx-auto mb-1" />
                          <div className="text-xs">{type.label}</div>
                        </button>
                      )
                    })}
                  </div>
                </div>

                {/* Form Fields */}
                <Input
                  label="اسم طريقة الدفع"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="مثال: بطاقة الراجحي الذهبية"
                />

                {formData.type === 'card' && (
                  <>
                    <Input
                      label="رقم البطاقة"
                      value={formData.cardNumber}
                      onChange={(e) => handleInputChange('cardNumber', e.target.value)}
                      placeholder="1234 5678 9012 3456"
                    />
                    
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        label="تاريخ الانتهاء"
                        value={formData.expiryDate}
                        onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                        placeholder="MM/YY"
                      />
                      <Input
                        label="CVV"
                        value={formData.cvv}
                        onChange={(e) => handleInputChange('cvv', e.target.value)}
                        placeholder="123"
                      />
                    </div>
                    
                    <Input
                      label="اسم حامل البطاقة"
                      value={formData.holderName}
                      onChange={(e) => handleInputChange('holderName', e.target.value)}
                      placeholder="الاسم كما يظهر على البطاقة"
                    />
                  </>
                )}

                {formData.type === 'bank' && (
                  <>
                    <Input
                      label="اسم البنك"
                      value={formData.bankName}
                      onChange={(e) => handleInputChange('bankName', e.target.value)}
                      placeholder="مثال: مصرف الراجحي"
                    />
                    <Input
                      label="رقم الآيبان (IBAN)"
                      value={formData.iban}
                      onChange={(e) => handleInputChange('iban', e.target.value)}
                      placeholder="SA00 0000 0000 0000 0000 0000"
                    />
                  </>
                )}

                <div className="flex space-x-3 rtl:space-x-reverse pt-4">
                  <Button
                    variant="ghost"
                    fullWidth
                    onClick={() => setShowAddForm(false)}
                  >
                    إلغاء
                  </Button>
                  <Button
                    variant="primary"
                    fullWidth
                    onClick={handleAddPaymentMethod}
                    disabled={!formData.name}
                  >
                    إضافة
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Payment Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="bg-gray-50">
            <div className="flex items-start space-x-3 rtl:space-x-reverse">
              <AlertCircle className="w-6 h-6 text-gray-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  معلومات مهمة
                </h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div>• يمكنك إضافة عدة طرق دفع واختيار الافتراضية</div>
                  <div>• جميع المعاملات محمية بتشفير SSL</div>
                  <div>• يتم حجز الأموال حتى إتمام الخدمة</div>
                  <div>• رسوم المنصة تُخصم تلقائياً من كل معاملة</div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
