'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Card from '@/components/ui/Card'
import { Mail, ArrowLeft, CheckCircle, ArrowRight } from 'lucide-react'
import { validateEmail } from '@/lib/utils'

export default function ForgotPasswordPage() {
  const router = useRouter()
  const [email, setEmail] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email) {
      setError('البريد الإلكتروني مطلوب')
      return
    }

    if (!validateEmail(email)) {
      setError('البريد الإلكتروني غير صحيح')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      setIsSubmitted(true)
    } catch (error) {
      setError('حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور')
    } finally {
      setIsLoading(false)
    }
  }

  if (isSubmitted) {
    return (
      <MobileLayout showHeader={false} showBottomNav={false}>
        <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 flex items-center justify-center p-6">
          <div className="w-full max-w-6xl mx-auto flex items-center justify-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
              className="w-full max-w-md"
            >
              <Card className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                
                <h1 className="text-2xl font-bold text-white mb-4">
                  تم إرسال الرابط!
                </h1>
                
                <p className="text-slate-300 mb-6 leading-relaxed">
                  تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني:
                </p>
                
                <div className="p-3 bg-slate-800 rounded-lg mb-6">
                  <p className="text-green-400 font-medium">{email}</p>
                </div>
                
                <div className="space-y-4 text-sm text-slate-400">
                  <p>• تحقق من صندوق الوارد وصندوق الرسائل المزعجة</p>
                  <p>• الرابط صالح لمدة 24 ساعة</p>
                  <p>• إذا لم تستلم الرسالة، يمكنك المحاولة مرة أخرى</p>
                </div>
                
                <div className="mt-8 space-y-3">
                  <Button
                    variant="primary"
                    size="lg"
                    fullWidth
                    onClick={() => router.push('/auth/login')}
                    rightIcon={<ArrowRight className="w-5 h-5" />}
                  >
                    العودة لتسجيل الدخول
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="lg"
                    fullWidth
                    onClick={() => setIsSubmitted(false)}
                  >
                    إرسال مرة أخرى
                  </Button>
                </div>
              </Card>
            </motion.div>
          </div>
        </div>
      </MobileLayout>
    )
  }

  return (
    <MobileLayout showHeader={false} showBottomNav={false}>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 flex items-center justify-center p-6">
        <div className="w-full max-w-6xl mx-auto flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="w-full max-w-md"
          >
            {/* Header */}
            <div className="flex items-center mb-8">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-slate-800 rounded-lg transition-colors mr-3"
              >
                <ArrowLeft className="w-5 h-5 text-slate-400" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  نسيت كلمة المرور؟
                </h1>
                <p className="text-slate-300">
                  سنرسل لك رابط إعادة تعيين كلمة المرور
                </p>
              </div>
            </div>

            <Card>
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Mail className="w-8 h-8 text-blue-600" />
                </div>
                <h2 className="text-xl font-bold text-white mb-2">
                  إعادة تعيين كلمة المرور
                </h2>
                <p className="text-slate-300 text-sm">
                  أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                    <p className="text-red-400 text-sm">{error}</p>
                  </div>
                )}

                <Input
                  label="البريد الإلكتروني"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  error={error}
                  leftIcon={<Mail className="w-5 h-5" />}
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                />

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  fullWidth
                  isLoading={isLoading}
                  rightIcon={<ArrowRight className="w-5 h-5" />}
                >
                  إرسال رابط إعادة التعيين
                </Button>
              </form>

              <div className="mt-6 text-center">
                <p className="text-slate-300 text-sm">
                  تذكرت كلمة المرور؟{' '}
                  <button
                    onClick={() => router.push('/auth/login')}
                    className="text-green-400 hover:text-green-300 font-medium"
                  >
                    تسجيل الدخول
                  </button>
                </p>
              </div>

              {/* Demo Note */}
              <div className="mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
                <h4 className="font-medium text-blue-400 mb-2">ملاحظة للتجربة:</h4>
                <p className="text-sm text-blue-300">
                  في النسخة التجريبية، لن يتم إرسال بريد إلكتروني فعلي. 
                  يمكنك استخدام أي بريد إلكتروني صحيح لرؤية شاشة التأكيد.
                </p>
              </div>
            </Card>

            <div className="mt-6 text-center">
              <button
                onClick={() => router.push('/')}
                className="text-slate-400 hover:text-slate-200 text-sm"
              >
                العودة إلى الصفحة الرئيسية
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </MobileLayout>
  )
}
