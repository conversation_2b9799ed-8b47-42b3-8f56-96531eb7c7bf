'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { ArrowLeft, FileText, Scale, AlertCircle, CheckCircle } from 'lucide-react'

export default function TermsPage() {
  const router = useRouter()

  const sections = [
    {
      icon: FileText,
      title: 'قبول الشروط',
      content: 'باستخدام منصة المؤثرين السعودية، فإنك توافق على الالتزام بهذه الشروط والأحكام. إذا كنت لا توافق على أي من هذه الشروط، يرجى عدم استخدام المنصة.'
    },
    {
      icon: Scale,
      title: 'الالتزامات القانونية',
      content: 'جميع المستخدمين ملزمون بالامتثال للقوانين واللوائح السعودية. أي نشاط غير قانوني أو مخالف للآداب العامة محظور تماماً.'
    },
    {
      icon: AlertCircle,
      title: 'المسؤوليات',
      content: 'كل مستخدم مسؤول عن المحتوى الذي ينشره والمعلومات التي يقدمها. المنصة غير مسؤولة عن أي أضرار ناتجة عن سوء الاستخدام.'
    },
    {
      icon: CheckCircle,
      title: 'ضمان الجودة',
      content: 'نلتزم بتوفير خدمة عالية الجودة، لكننا لا نضمن عدم انقطاع الخدمة أو خلوها من الأخطاء. نعمل باستمرار على تحسين المنصة.'
    }
  ]

  return (
    <MobileLayout>
      <div className="min-h-screen bg-slate-900">
        <div className="max-w-4xl mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-slate-800 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-slate-400" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-white">الشروط والأحكام</h1>
              <p className="text-slate-400">قواعد استخدام المنصة</p>
            </div>
          </div>

        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Scale className="w-8 h-8 text-blue-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                شروط الاستخدام
              </h2>
              <p className="text-gray-600 leading-relaxed">
                هذه الشروط والأحكام تحكم استخدامك لمنصة المؤثرين السعودية.
                يرجى قراءتها بعناية قبل استخدام خدماتنا.
              </p>
            </div>
          </Card>
        </motion.div>

        {/* Main Sections */}
        <div className="space-y-4">
          {sections.map((section, index) => {
            const Icon = section.icon
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
              >
                <Card>
                  <div className="flex items-start space-x-4 rtl:space-x-reverse">
                    <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Icon className="w-6 h-6 text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-2">
                        {section.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {section.content}
                      </p>
                    </div>
                  </div>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Detailed Terms */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <h3 className="font-semibold text-gray-900 mb-4">شروط التسجيل</h3>
            <div className="space-y-3 text-gray-600">
              <div>• يجب أن تكون 18 سنة أو أكثر للتسجيل</div>
              <div>• تقديم معلومات صحيحة ومحدثة</div>
              <div>• عدم إنشاء حسابات متعددة لنفس الشخص</div>
              <div>• الحفاظ على سرية بيانات الدخول</div>
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card>
            <h3 className="font-semibold text-gray-900 mb-4">السلوك المقبول</h3>
            <div className="space-y-3 text-gray-600">
              <div>• احترام جميع المستخدمين والتعامل بأدب</div>
              <div>• عدم نشر محتوى مسيء أو غير لائق</div>
              <div>• الالتزام بالمواعيد المتفق عليها</div>
              <div>• تقديم خدمات عالية الجودة</div>
              <div>• عدم انتهاك حقوق الملكية الفكرية</div>
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card>
            <h3 className="font-semibold text-gray-900 mb-4">المدفوعات والرسوم</h3>
            <div className="space-y-3 text-gray-600">
              <div>• جميع الأسعار بالريال السعودي شاملة ضريبة القيمة المضافة</div>
              <div>• المدفوعات تتم عبر وسائل دفع آمنة ومعتمدة</div>
              <div>• الأموال محجوزة حتى إتمام الخدمة بنجاح</div>
              <div>• رسوم المنصة تُخصم تلقائياً من كل معاملة</div>
              <div>• سياسة استرداد واضحة للحالات المبررة</div>
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
        >
          <Card>
            <h3 className="font-semibold text-gray-900 mb-4">إنهاء الحساب</h3>
            <div className="space-y-3 text-gray-600">
              <div>• يمكنك إلغاء حسابك في أي وقت</div>
              <div>• نحتفظ بالحق في إيقاف الحسابات المخالفة</div>
              <div>• البيانات المحذوفة لا يمكن استردادها</div>
              <div>• الالتزامات المالية تبقى سارية حتى بعد الإلغاء</div>
            </div>
          </Card>
        </motion.div>

        {/* Disclaimer */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
        >
          <Card className="bg-yellow-50 border-yellow-200">
            <div className="flex items-start space-x-3 rtl:space-x-reverse">
              <AlertCircle className="w-6 h-6 text-yellow-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-semibold text-yellow-900 mb-2">
                  إخلاء المسؤولية
                </h3>
                <p className="text-yellow-800 text-sm leading-relaxed">
                  المنصة تعمل كوسيط بين التجار والمؤثرين. نحن غير مسؤولين عن جودة الخدمات
                  المقدمة أو أي نزاعات قد تنشأ بين الأطراف. ننصح بقراءة جميع التفاصيل قبل إبرام أي اتفاق.
                </p>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Contact */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.1 }}
        >
          <Card className="bg-green-50 border-green-200">
            <div className="text-center">
              <h3 className="font-semibold text-green-900 mb-2">
                أسئلة حول الشروط؟
              </h3>
              <p className="text-green-700 mb-4">
                إذا كان لديك أي استفسارات حول الشروط والأحكام، فريق الدعم جاهز لمساعدتك
              </p>
              <Button variant="primary" size="sm">
                تواصل مع الدعم
              </Button>
            </div>
          </Card>
        </motion.div>

        {/* Last Updated */}
        <div className="text-center text-sm text-gray-500">
          آخر تحديث: يناير 2024
        </div>
      </div>
    </MobileLayout>
  )
}
