import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: number): string {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR',
    minimumFractionDigits: 0,
  }).format(price)
}

export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

export function getStatusColor(status: string): string {
  switch (status) {
    case 'pending':
      return 'status-pending'
    case 'approved':
      return 'status-approved'
    case 'rejected':
      return 'status-rejected'
    case 'completed':
      return 'status-completed'
    default:
      return 'status-pending'
  }
}

export function getStatusText(status: string): string {
  switch (status) {
    case 'pending':
      return 'قيد المراجعة'
    case 'approved':
      return 'مقبول'
    case 'rejected':
      return 'مرفوض'
    case 'completed':
      return 'مكتمل'
    default:
      return 'قيد المراجعة'
  }
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validatePhone(phone: string): boolean {
  const phoneRegex = /^(\+966|0)?[5][0-9]{8}$/
  return phoneRegex.test(phone)
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}

export function formatDateTime(date: Date): string {
  return new Intl.DateTimeFormat('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substr(0, maxLength) + '...'
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export function getImageUrl(path: string): string {
  if (isValidUrl(path)) return path
  return `/images/${path}`
}

export function calculateAge(birthDate: Date): number {
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  
  return age
}

export function getAgeGroup(age: number): string {
  if (age < 18) return 'أقل من 18'
  if (age < 25) return '18-24'
  if (age < 35) return '25-34'
  if (age < 45) return '35-44'
  if (age < 55) return '45-54'
  return '55+'
}

export function getSaudiCities(): string[] {
  return [
    'الرياض',
    'جدة',
    'مكة المكرمة',
    'المدينة المنورة',
    'الدمام',
    'الخبر',
    'الظهران',
    'تبوك',
    'بريدة',
    'خميس مشيط',
    'حائل',
    'نجران',
    'الطائف',
    'الجبيل',
    'ينبع',
    'أبها',
    'عرعر',
    'سكاكا',
    'جازان',
    'القطيف',
    'الأحساء',
    'الباحة',
    'القريات',
    'الرس',
    'وادي الدواسر',
    'صبيا',
    'محايل عسير',
    'الزلفي',
    'المجمعة',
    'الخرج'
  ]
}

export function getPlatforms(): Array<{id: string, name: string, icon: string}> {
  return [
    { id: 'snapchat', name: 'سناب شات', icon: '👻' },
    { id: 'instagram', name: 'إنستغرام', icon: '📷' },
    { id: 'tiktok', name: 'تيك توك', icon: '🎵' },
    { id: 'youtube', name: 'يوتيوب', icon: '📺' },
    { id: 'twitter', name: 'تويتر', icon: '🐦' },
    { id: 'linkedin', name: 'لينكد إن', icon: '💼' },
  ]
}

export function getServiceTypes(): Array<{id: string, name: string, description: string}> {
  return [
    { id: 'store_visit', name: 'زيارة المحل', description: 'زيارة المحل وتصوير محتوى' },
    { id: 'full_story', name: 'ستوري كامل', description: 'ستوري كامل على سناب شات' },
    { id: 'single_snap', name: 'صورة سناب', description: 'صورة واحدة على سناب شات' },
    { id: 'video', name: 'فيديو', description: 'فيديو ترويجي' },
    { id: 'instagram_reel', name: 'ريل إنستغرام', description: 'ريل على إنستغرام' },
    { id: 'tiktok_video', name: 'فيديو تيك توك', description: 'فيديو على تيك توك' },
    { id: 'youtube_video', name: 'فيديو يوتيوب', description: 'فيديو على يوتيوب' },
  ]
}

export function getInterests(): string[] {
  return [
    'الموضة والأزياء',
    'الجمال والعناية',
    'الطعام والمطاعم',
    'السفر والسياحة',
    'التقنية والألعاب',
    'الرياضة واللياقة',
    'الصحة والعافية',
    'التعليم والثقافة',
    'الفن والإبداع',
    'السيارات',
    'العقارات',
    'الأطفال والعائلة',
    'الأعمال والمال',
    'الترفيه والكوميديا',
    'الطبخ والحلويات',
    'الديكور والمنزل'
  ]
}
