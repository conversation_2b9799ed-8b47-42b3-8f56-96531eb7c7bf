'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth, useUI } from '@/lib/store'
import Header from './Header'
import BottomNavigation from './BottomNavigation'
import { cn } from '@/lib/utils'

interface MobileLayoutProps {
  children: React.ReactNode
  showHeader?: boolean
  showBottomNav?: boolean
  className?: string
}

const MobileLayout: React.FC<MobileLayoutProps> = ({
  children,
  showHeader = true,
  showBottomNav = true,
  className
}) => {
  const { isAuthenticated } = useAuth()
  const { isLoading, error } = useUI()
  
  return (
    <div className="app-container">
      <div className="mobile-container">
        {/* Header */}
        <AnimatePresence>
          {showHeader && (
            <motion.div
              initial={{ y: -100, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -100, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Header />
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Main Content */}
        <main className={cn(
          'flex-1 overflow-y-auto',
          showHeader && 'pt-16',
          showBottomNav && isAuthenticated && 'pb-20',
          className
        )}>
          <AnimatePresence mode="wait">
            {isLoading ? (
              <motion.div
                key="loading"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex items-center justify-center min-h-screen"
              >
                <div className="text-center">
                  <div className="loading-spinner mx-auto mb-4" />
                  <p className="text-gray-600">جاري التحميل...</p>
                </div>
              </motion.div>
            ) : error ? (
              <motion.div
                key="error"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="flex items-center justify-center min-h-screen p-4"
              >
                <div className="text-center">
                  <div className="text-red-500 text-6xl mb-4">⚠️</div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">
                    حدث خطأ
                  </h2>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <button
                    onClick={() => window.location.reload()}
                    className="btn-primary"
                  >
                    إعادة المحاولة
                  </button>
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="content"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                {children}
              </motion.div>
            )}
          </AnimatePresence>
        </main>
        
        {/* Bottom Navigation */}
        <AnimatePresence>
          {showBottomNav && isAuthenticated && (
            <motion.div
              initial={{ y: 100, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: 100, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md"
            >
              <BottomNavigation />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default MobileLayout
