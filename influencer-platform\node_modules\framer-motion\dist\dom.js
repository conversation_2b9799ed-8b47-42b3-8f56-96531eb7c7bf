!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={})}(this,(function(t){"use strict";function e(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const s=(t,e,n)=>n>e?e:n<t?t:n;let i=()=>{};const r={},o=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function a(t){return"object"==typeof t&&null!==t}const l=t=>/^0[^.\s]+$/u.test(t);function u(t){let e;return()=>(void 0===e&&(e=t()),e)}const c=t=>t,h=(t,e)=>n=>e(t(n)),d=(...t)=>t.reduce(h),p=(t,e,n)=>{const s=e-t;return 0===s?1:(n-t)/s};class f{constructor(){this.subscriptions=[]}add(t){return e(this.subscriptions,t),()=>n(this.subscriptions,t)}notify(t,e,n){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,n);else for(let i=0;i<s;i++){const s=this.subscriptions[i];s&&s(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const m=t=>1e3*t,g=t=>t/1e3;function y(t,e){return e?t*(1e3/e):0}const v=new Set;const w=(t,e,n)=>{const s=e-t;return((n-t)%s+s)%s+t},b=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function T(t,e,n,s){if(t===e&&n===s)return c;const i=e=>function(t,e,n,s,i){let r,o,a=0;do{o=e+(n-e)/2,r=b(o,s,i)-t,r>0?n=o:e=o}while(Math.abs(r)>1e-7&&++a<12);return o}(e,0,1,t,n);return t=>0===t||1===t?t:b(i(t),e,s)}const x=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,V=t=>e=>1-t(1-e),M=T(.33,1.53,.69,.99),S=V(M),A=x(S),k=t=>(t*=2)<1?.5*S(t):.5*(2-Math.pow(2,-10*(t-1))),E=t=>1-Math.sin(Math.acos(t)),P=V(E),C=x(E),O=T(.42,0,1,1),F=T(0,0,.58,1),R=T(.42,0,.58,1);const B=t=>Array.isArray(t)&&"number"!=typeof t[0];function D(t,e){return B(t)?t[w(0,t.length,e)]:t}const L=t=>Array.isArray(t)&&"number"==typeof t[0],I={linear:c,easeIn:O,easeInOut:R,easeOut:F,circIn:E,circInOut:C,circOut:P,backIn:S,backInOut:A,backOut:M,anticipate:k},W=t=>{if(L(t)){t.length;const[e,n,s,i]=t;return T(e,n,s,i)}return"string"==typeof t?I[t]:t},N=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],j={value:null,addProjectionMetrics:null};function K(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,a=N.reduce(((t,n)=>(t[n]=function(t,e){let n=new Set,s=new Set,i=!1,r=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(c.schedule(e),t()),l++,e(a)}const c={schedule:(t,e=!1,r=!1)=>{const a=r&&i?n:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{a=t,i?r=!0:(i=!0,[n,s]=[s,n],n.forEach(u),e&&j.value&&j.value.frameloop[e].push(l),l=0,n.clear(),i=!1,r&&(r=!1,c.process(t)))}};return c}(o,e?n:void 0),t)),{}),{setup:l,read:u,resolveKeyframes:c,preUpdate:h,update:d,preRender:p,render:f,postRender:m}=a,g=()=>{const o=r.useManualTiming?i.timestamp:performance.now();n=!1,r.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,l.process(i),u.process(i),c.process(i),h.process(i),d.process(i),p.process(i),f.process(i),m.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(g))};return{schedule:N.reduce(((e,r)=>{const o=a[r];return e[r]=(e,r=!1,a=!1)=>(n||(n=!0,s=!0,i.isProcessing||t(g)),o.schedule(e,r,a)),e}),{}),cancel:t=>{for(let e=0;e<N.length;e++)a[N[e]].cancel(t)},state:i,steps:a}}const{schedule:$,cancel:z,state:U,steps:Y}=K("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:c,!0);let X;function H(){X=void 0}const q={now:()=>(void 0===X&&q.set(U.isProcessing||r.useManualTiming?U.timestamp:performance.now()),X),set:t=>{X=t,queueMicrotask(H)}},G={layout:0,mainThread:0,waapi:0},Z=t=>e=>"string"==typeof e&&e.startsWith(t),_=Z("--"),J=Z("var(--"),Q=t=>!!J(t)&&tt.test(t.split("/*")[0].trim()),tt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,et={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},nt={...et,transform:t=>s(0,1,t)},st={...et,default:1},it=t=>Math.round(1e5*t)/1e5,rt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const ot=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,at=(t,e)=>n=>Boolean("string"==typeof n&&ot.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),lt=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[i,r,o,a]=s.match(rt);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ut={...et,transform:t=>Math.round((t=>s(0,255,t))(t))},ct={test:at("rgb","red"),parse:lt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+ut.transform(t)+", "+ut.transform(e)+", "+ut.transform(n)+", "+it(nt.transform(s))+")"};const ht={test:at("#"),parse:function(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}},transform:ct.transform},dt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),pt=dt("deg"),ft=dt("%"),mt=dt("px"),gt=dt("vh"),yt=dt("vw"),vt=(()=>({...ft,parse:t=>ft.parse(t)/100,transform:t=>ft.transform(100*t)}))(),wt={test:at("hsl","hue"),parse:lt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+ft.transform(it(e))+", "+ft.transform(it(n))+", "+it(nt.transform(s))+")"},bt={test:t=>ct.test(t)||ht.test(t)||wt.test(t),parse:t=>ct.test(t)?ct.parse(t):wt.test(t)?wt.parse(t):ht.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ct.transform(t):wt.transform(t)},Tt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const xt="number",Vt="color",Mt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function St(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const o=e.replace(Mt,(t=>(bt.test(t)?(s.color.push(r),i.push(Vt),n.push(bt.parse(t))):t.startsWith("var(")?(s.var.push(r),i.push("var"),n.push(t)):(s.number.push(r),i.push(xt),n.push(parseFloat(t))),++r,"${}"))).split("${}");return{values:n,split:o,indexes:s,types:i}}function At(t){return St(t).values}function kt(t){const{split:e,types:n}=St(t),s=e.length;return t=>{let i="";for(let r=0;r<s;r++)if(i+=e[r],void 0!==t[r]){const e=n[r];i+=e===xt?it(t[r]):e===Vt?bt.transform(t[r]):t[r]}return i}}const Et=t=>"number"==typeof t?0:t;const Pt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(rt)?.length||0)+(t.match(Tt)?.length||0)>0},parse:At,createTransformer:kt,getAnimatableNone:function(t){const e=At(t);return kt(t)(e.map(Et))}};function Ct(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Ot({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let i=0,r=0,o=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,a=2*n-s;i=Ct(a,s,t+1/3),r=Ct(a,s,t),o=Ct(a,s,t-1/3)}else i=r=o=n;return{red:Math.round(255*i),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}function Ft(t,e){return n=>n>0?e:t}const Rt=(t,e,n)=>t+(e-t)*n,Bt=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},Dt=[ht,ct,wt];function Lt(t){const e=(n=t,Dt.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let s=e.parse(t);return e===wt&&(s=Ot(s)),s}const It=(t,e)=>{const n=Lt(t),s=Lt(e);if(!n||!s)return Ft(t,e);const i={...n};return t=>(i.red=Bt(n.red,s.red,t),i.green=Bt(n.green,s.green,t),i.blue=Bt(n.blue,s.blue,t),i.alpha=Rt(n.alpha,s.alpha,t),ct.transform(i))},Wt=new Set(["none","hidden"]);function Nt(t,e){return Wt.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function jt(t,e){return n=>Rt(t,e,n)}function Kt(t){return"number"==typeof t?jt:"string"==typeof t?Q(t)?Ft:bt.test(t)?It:Ut:Array.isArray(t)?$t:"object"==typeof t?bt.test(t)?It:zt:Ft}function $t(t,e){const n=[...t],s=n.length,i=t.map(((t,n)=>Kt(t)(t,e[n])));return t=>{for(let e=0;e<s;e++)n[e]=i[e](t);return n}}function zt(t,e){const n={...t,...e},s={};for(const i in n)void 0!==t[i]&&void 0!==e[i]&&(s[i]=Kt(t[i])(t[i],e[i]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const Ut=(t,e)=>{const n=Pt.createTransformer(e),s=St(t),i=St(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Wt.has(t)&&!i.values.length||Wt.has(e)&&!s.values.length?Nt(t,e):d($t(function(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],o=t.indexes[r][s[r]],a=t.values[o]??0;n[i]=a,s[r]++}return n}(s,i),i.values),n):Ft(t,e)};function Yt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Rt(t,e,n);return Kt(t)(t,e)}const Xt=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>$.update(e,t),stop:()=>z(e),now:()=>U.isProcessing?U.timestamp:q.now()}},Ht=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let e=0;e<i;e++)s+=t(e/(i-1))+", ";return`linear(${s.substring(0,s.length-2)})`},qt=2e4;function Gt(t){let e=0;let n=t.next(e);for(;!n.done&&e<qt;)e+=50,n=t.next(e);return e>=qt?1/0:e}function Zt(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(Gt(s),qt);return{type:"keyframes",ease:t=>s.next(i*t).value/e,duration:g(i)}}function _t(t,e,n){const s=Math.max(e-5,0);return y(n-t(s),e-s)}const Jt=100,Qt=10,te=1,ee=0,ne=800,se=.3,ie=.3,re={granular:.01,default:2},oe={granular:.005,default:.5},ae=.01,le=10,ue=.05,ce=1,he=.001;function de({duration:t=ne,bounce:e=se,velocity:n=ee,mass:i=te}){let r,o,a=1-e;a=s(ue,ce,a),t=s(ae,le,g(t)),a<1?(r=e=>{const s=e*a,i=s*t,r=s-n,o=fe(e,a),l=Math.exp(-i);return he-r/o*l},o=e=>{const s=e*a*t,i=s*n+n,o=Math.pow(a,2)*Math.pow(e,2)*t,l=Math.exp(-s),u=fe(Math.pow(e,2),a);return(-r(e)+he>0?-1:1)*((i-o)*l)/u}):(r=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const l=function(t,e,n){let s=n;for(let n=1;n<pe;n++)s-=t(s)/e(s);return s}(r,o,5/t);if(t=m(t),isNaN(l))return{stiffness:Jt,damping:Qt,duration:t};{const e=Math.pow(l,2)*i;return{stiffness:e,damping:2*a*Math.sqrt(i*e),duration:t}}}const pe=12;function fe(t,e){return t*Math.sqrt(1-e*e)}const me=["duration","bounce"],ge=["stiffness","damping","mass"];function ye(t,e){return e.some((e=>void 0!==t[e]))}function ve(t=ie,e=se){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:r}=n;const o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:c,mass:h,duration:d,velocity:p,isResolvedFromDuration:f}=function(t){let e={velocity:ee,stiffness:Jt,damping:Qt,mass:te,isResolvedFromDuration:!1,...t};if(!ye(t,ge)&&ye(t,me))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),r=i*i,o=2*s(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:te,stiffness:r,damping:o}}else{const n=de(t);e={...e,...n,mass:te},e.isResolvedFromDuration=!0}return e}({...n,velocity:-g(n.velocity||0)}),y=p||0,v=c/(2*Math.sqrt(u*h)),w=a-o,b=g(Math.sqrt(u/h)),T=Math.abs(w)<5;let x;if(i||(i=T?re.granular:re.default),r||(r=T?oe.granular:oe.default),v<1){const t=fe(b,v);x=e=>{const n=Math.exp(-v*b*e);return a-n*((y+v*b*w)/t*Math.sin(t*e)+w*Math.cos(t*e))}}else if(1===v)x=t=>a-Math.exp(-b*t)*(w+(y+b*w)*t);else{const t=b*Math.sqrt(v*v-1);x=e=>{const n=Math.exp(-v*b*e),s=Math.min(t*e,300);return a-n*((y+v*b*w)*Math.sinh(s)+t*w*Math.cosh(s))/t}}const V={calculatedDuration:f&&d||null,next:t=>{const e=x(t);if(f)l.done=t>=d;else{let n=0===t?y:0;v<1&&(n=0===t?m(y):_t(x,t,e));const s=Math.abs(n)<=i,o=Math.abs(a-e)<=r;l.done=s&&o}return l.value=l.done?a:e,l},toString:()=>{const t=Math.min(Gt(V),qt),e=Ht((e=>V.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return V}function we({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let f=n*e;const m=h+f,g=void 0===o?m:o(m);g!==m&&(f=g-h);const y=t=>-f*Math.exp(-t/s),v=t=>g+y(t),w=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let b,T;const x=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(b=t,T=ve({keyframes:[d.value,p(d.value)],velocity:_t(v,t,d.value),damping:i,stiffness:r,restDelta:u,restSpeed:c}))};return x(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==b||(e=!0,w(t),x(t)),void 0!==b&&t>=b?T.next(t-b):(!e&&w(t),d)}}}function be(t,e,{clamp:n=!0,ease:i,mixer:o}={}){const a=t.length;if(e.length,1===a)return()=>e[0];if(2===a&&e[0]===e[1])return()=>e[1];const l=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),e=[...e].reverse());const u=function(t,e,n){const s=[],i=n||r.mix||Yt,o=t.length-1;for(let n=0;n<o;n++){let r=i(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||c:e;r=d(t,r)}s.push(r)}return s}(e,i,o),h=u.length,f=n=>{if(l&&n<t[0])return e[0];let s=0;if(h>1)for(;s<t.length-2&&!(n<t[s+1]);s++);const i=p(t[s],t[s+1],n);return u[s](i)};return n?e=>f(s(t[0],t[a-1],e)):f}function Te(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=p(0,e,s);t.push(Rt(n,1,i))}}function xe(t){const e=[0];return Te(e,t.length-1),e}function Ve(t,e){return t.map((t=>t*e))}function Me(t,e){return t.map((()=>e||R)).splice(0,t.length-1)}function Se({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=B(s)?s.map(W):W(s),r={done:!1,value:e[0]},o=be(Ve(n&&n.length===e.length?n:xe(e),t),e,{ease:Array.isArray(i)?i:Me(e,i)});return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}ve.applyToOptions=t=>{const e=Zt(t,100,ve);return t.ease=e.ease,t.duration=m(e.duration),t.type="keyframes",t};const Ae=t=>null!==t;function ke(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(Ae),o=i<0||e&&"loop"!==n&&e%2==1?0:r.length-1;return o&&void 0!==s?s:r[o]}const Ee={decay:we,inertia:we,tween:Se,keyframes:Se,spring:ve};function Pe(t){"string"==typeof t.type&&(t.type=Ee[t.type])}class Ce{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Oe=t=>t/100;class Fe extends Ce{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==q.now()&&this.tick(q.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},G.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Pe(t);const{type:e=Se,repeat:n=0,repeatDelay:s=0,repeatType:i,velocity:r=0}=t;let{keyframes:o}=t;const a=e||Se;a!==Se&&"number"!=typeof o[0]&&(this.mixKeyframes=d(Oe,Yt(o[0],o[1])),o=[0,100]);const l=a({...t,keyframes:o});"mirror"===i&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=Gt(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+s,this.totalDuration=this.resolvedDuration*(n+1)-s,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:r,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:l}=this;if(null===this.startTime)return n.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:d,repeatDelay:p,type:f,onUpdate:m,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>i;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let w=this.currentTime,b=n;if(h){const t=Math.min(this.currentTime,i)/a;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,h+1);Boolean(e%2)&&("reverse"===d?(n=1-n,p&&(n-=p/a)):"mirror"===d&&(b=o)),w=s(0,1,n)*a}const T=v?{done:!1,value:c[0]}:b.next(w);r&&(T.value=r(T.value));let{done:x}=T;v||null===l||(x=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const V=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return V&&f!==we&&(T.value=ke(c,this.options,g,this.speed)),m&&m(T.value),V&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return g(this.calculatedDuration)}get time(){return g(this.currentTime)}set time(t){t=m(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(q.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=g(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=Xt,startTime:e}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(q.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,G.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function Re(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Be=t=>180*t/Math.PI,De=t=>{const e=Be(Math.atan2(t[1],t[0]));return Ie(e)},Le={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:De,rotateZ:De,skewX:t=>Be(Math.atan(t[1])),skewY:t=>Be(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ie=t=>((t%=360)<0&&(t+=360),t),We=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Ne=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),je={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:We,scaleY:Ne,scale:t=>(We(t)+Ne(t))/2,rotateX:t=>Ie(Be(Math.atan2(t[6],t[5]))),rotateY:t=>Ie(Be(Math.atan2(-t[2],t[0]))),rotateZ:De,rotate:De,skewX:t=>Be(Math.atan(t[4])),skewY:t=>Be(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Ke(t){return t.includes("scale")?1:0}function $e(t,e){if(!t||"none"===t)return Ke(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=je,i=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Le,i=e}if(!i)return Ke(e);const r=s[e],o=i[1].split(",").map(Ue);return"function"==typeof r?r(o):o[r]}const ze=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return $e(n,e)};function Ue(t){return parseFloat(t.trim())}const Ye=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Xe=(()=>new Set(Ye))(),He=t=>t===et||t===mt,qe=new Set(["x","y","z"]),Ge=Ye.filter((t=>!qe.has(t)));const Ze={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>$e(e,"x"),y:(t,{transform:e})=>$e(e,"y")};Ze.translateX=Ze.x,Ze.translateY=Ze.y;const _e=new Set;let Je=!1,Qe=!1,tn=!1;function en(){if(Qe){const t=Array.from(_e).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return Ge.forEach((n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{t.getValue(e)?.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}Qe=!1,Je=!1,_e.forEach((t=>t.complete(tn))),_e.clear()}function nn(){_e.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(Qe=!0)}))}function sn(){tn=!0,nn(),en(),tn=!1}class rn{constructor(t,e,n,s,i,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=i,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(_e.add(this),Je||(Je=!0,$.read(nn),$.resolveKeyframes(en))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(null===t[0]){const i=s?.get(),r=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){const s=n.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===i&&s.set(t[0])}Re(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),_e.delete(this)}cancel(){"scheduled"===this.state&&(_e.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const on=t=>t.startsWith("--");function an(t,e,n){on(e)?t.style.setProperty(e,n):t.style[e]=n}const ln=u((()=>void 0!==window.ScrollTimeline)),un={};function cn(t,e){const n=u(t);return()=>un[e]??n()}const hn=cn((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),dn=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,pn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:dn([0,.65,.55,1]),circOut:dn([.55,0,1,.45]),backIn:dn([.31,.01,.66,-.59]),backOut:dn([.33,1.53,.69,.99])};function fn(t,e){return t?"function"==typeof t?hn()?Ht(t,e):"ease-out":L(t)?dn(t):Array.isArray(t)?t.map((t=>fn(t,e)||pn.easeOut)):pn[t]:void 0}function mn(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[e]:n};l&&(c.offset=l);const h=fn(a,i);Array.isArray(h)&&(c.easing=h),j.value&&G.waapi++;const d={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};u&&(d.pseudoElement=u);const p=t.animate(c,d);return j.value&&p.finished.finally((()=>{G.waapi--})),p}function gn(t){return"function"==typeof t&&"applyToOptions"in t}function yn({type:t,...e}){return gn(t)&&hn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class vn extends Ce{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:s,pseudoElement:i,allowFlatten:r=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=Boolean(i),this.allowFlatten=r,this.options=t,t.type;const l=yn(t);this.animation=mn(e,n,s,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const t=ke(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):an(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return g(Number(t))}get time(){return g(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=m(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&ln()?(this.animation.timeline=t,c):e(this)}}const wn={anticipate:k,backInOut:A,circInOut:C};function bn(t){"string"==typeof t.ease&&t.ease in wn&&(t.ease=wn[t.ease])}class Tn extends vn{constructor(t){bn(t),Pe(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:s,element:i,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const o=new Fe({...r,autoplay:!1}),a=m(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}const xn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Pt.test(t)&&"0"!==t||t.startsWith("url(")));function Vn(t){return a(t)&&"offsetHeight"in t}const Mn=new Set(["opacity","clipPath","filter","transform"]),Sn=u((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));function An(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:o}=t;if(!Vn(e?.owner?.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Sn()&&n&&Mn.has(n)&&("transform"!==n||!l)&&!a&&!s&&"mirror"!==i&&0!==r&&"inertia"!==o}class kn extends Ce{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r="loop",keyframes:o,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=q.now();const h={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:i,repeatType:r,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||rn;this.keyframeResolver=new d(o,((t,e,n)=>this.onKeyframesResolved(t,e,h,!n)),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,s){this.keyframeResolver=void 0;const{name:i,type:o,velocity:a,delay:l,isHandoff:u,onUpdate:h}=n;this.resolvedAt=q.now(),function(t,e,n,s){const i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;const r=t[t.length-1],o=xn(i,e),a=xn(r,e);return!(!o||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||gn(n))&&s)}(t,i,o,a)||(!r.instantAnimations&&l||h?.(ke(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const d={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},p=!u&&An(d)?new Tn({...d,element:d.motionValue.owner.current}):new Fe(d);p.finished.then((()=>this.notifyFinished())).catch(c),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||(this.keyframeResolver?.resume(),sn()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class En{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map((t=>t.finished)))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map((e=>e.attachTimeline(t)));return()=>{e.forEach(((t,e)=>{t&&t(),this.animations[e].stop()}))}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach((e=>e[t]()))}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class Pn extends En{then(t,e){return this.finished.finally(t).then((()=>{}))}}class Cn extends vn{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const On=new WeakMap,Fn=(t,e="")=>`${t}:${e}`;function Rn(t){const e=On.get(t)||new Map;return On.set(t,e),e}const Bn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Dn(t){const e=Bn.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function Ln(t,e,n=1){const[s,i]=Dn(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const t=r.trim();return o(t)?parseFloat(t):t}return Q(i)?Ln(i,e,n+1):i}function In(t,e){return t?.[e]??t?.default??t}const Wn=new Set(["width","height","top","left","right","bottom",...Ye]),Nn=t=>e=>e.test(t),jn=[et,mt,ft,pt,yt,gt,{test:t=>"auto"===t,parse:t=>t}],Kn=t=>jn.find(Nn(t));const $n=new Set(["brightness","contrast","saturate","opacity"]);function zn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(rt)||[];if(!s)return t;const i=n.replace(s,"");let r=$n.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Un=/\b([a-z-]*)\(.*?\)/gu,Yn={...Pt,getAnimatableNone:t=>{const e=t.match(Un);return e?e.map(zn).join(" "):t}},Xn={...et,transform:Math.round},Hn={rotate:pt,rotateX:pt,rotateY:pt,rotateZ:pt,scale:st,scaleX:st,scaleY:st,scaleZ:st,skew:pt,skewX:pt,skewY:pt,distance:mt,translateX:mt,translateY:mt,translateZ:mt,x:mt,y:mt,z:mt,perspective:mt,transformPerspective:mt,opacity:nt,originX:vt,originY:vt,originZ:mt},qn={borderWidth:mt,borderTopWidth:mt,borderRightWidth:mt,borderBottomWidth:mt,borderLeftWidth:mt,borderRadius:mt,radius:mt,borderTopLeftRadius:mt,borderTopRightRadius:mt,borderBottomRightRadius:mt,borderBottomLeftRadius:mt,width:mt,maxWidth:mt,height:mt,maxHeight:mt,top:mt,right:mt,bottom:mt,left:mt,padding:mt,paddingTop:mt,paddingRight:mt,paddingBottom:mt,paddingLeft:mt,margin:mt,marginTop:mt,marginRight:mt,marginBottom:mt,marginLeft:mt,backgroundPositionX:mt,backgroundPositionY:mt,...Hn,zIndex:Xn,fillOpacity:nt,strokeOpacity:nt,numOctaves:Xn},Gn={...qn,color:bt,backgroundColor:bt,outlineColor:bt,fill:bt,stroke:bt,borderColor:bt,borderTopColor:bt,borderRightColor:bt,borderBottomColor:bt,borderLeftColor:bt,filter:Yn,WebkitFilter:Yn},Zn=t=>Gn[t];function _n(t,e){let n=Zn(t);return n!==Yn&&(n=Pt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Jn=new Set(["auto","none","0"]);class Qn extends rn{constructor(t,e,n,s,i){super(t,e,n,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let s=t[n];if("string"==typeof s&&(s=s.trim(),Q(s))){const i=Ln(s,e.current);void 0!==i&&(t[n]=i),n===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!Wn.has(n)||2!==t.length)return;const[s,i]=t,r=Kn(s),o=Kn(i);if(r!==o)if(He(r)&&He(o))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else Ze[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||l(s)))&&n.push(e);var s;n.length&&function(t,e,n){let s,i=0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!Jn.has(e)&&St(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=_n(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ze[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const i=n.length-1,r=n[i];n[i]=Ze[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,n])=>{t.getValue(e).set(n)})),this.resolveNoneKeyframes()}}const ts=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);function es(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&ts.has(e)&&(t[n]=t[n]+"px")}const ns=u((()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0})),ss=new Set(["opacity","clipPath","filter","transform"]),is={current:void 0};class rs{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=q.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=q.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new f);const n=this.events[t].add(e);return"change"===t?()=>{n(),$.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return is.current&&is.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=q.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return y(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function os(t,e){return new rs(t,e)}function as(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}function ls(t){return(e,n)=>{const s=as(e),i=[];for(const e of s){const s=t(e,n);i.push(s)}return()=>{for(const t of i)t()}}}const us=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class cs{constructor(){this.latest={},this.values=new Map}set(t,e,n,s,i=!0){const r=this.values.get(t);r&&r.onRemove();const o=()=>{const s=e.get();this.latest[t]=i?us(s,qn[t]):s,n&&$.render(n)};o();const a=e.on("change",o);s&&e.addDependent(s);const l=()=>{a(),n&&z(n),this.values.delete(t),s&&e.removeDependent(s)};return this.values.set(t,{value:e,onRemove:l}),l}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}function hs(t){const e=new WeakMap,n=[];return(s,i)=>{const r=e.get(s)??new cs;e.set(s,r);for(const e in i){const o=i[e],a=t(s,r,e,o);n.push(a)}return()=>{for(const t of n)t()}}}const ds={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const ps=new Set(["originX","originY","originZ"]),fs=(t,e,n,s)=>{let i,r;return Xe.has(n)?(e.get("transform")||e.set("transform",new rs("none"),(()=>{t.style.transform=function(t){let e="",n=!0;for(let s=0;s<Ye.length;s++){const i=Ye[s],r=t.latest[i];if(void 0===r)continue;let o=!0;o="number"==typeof r?r===(i.startsWith("scale")?1:0):0===parseFloat(r),o||(n=!1,e+=`${ds[i]||i}(${t.latest[i]}) `)}return n?"none":e.trim()}(e)})),r=e.get("transform")):ps.has(n)?(e.get("transformOrigin")||e.set("transformOrigin",new rs(""),(()=>{const n=e.latest.originX??"50%",s=e.latest.originY??"50%",i=e.latest.originZ??0;t.style.transformOrigin=`${n} ${s} ${i}`})),r=e.get("transformOrigin")):i=on(n)?()=>{t.style.setProperty(n,e.latest[n])}:()=>{t.style[n]=e.latest[n]},e.set(n,s,i,r)},ms=ls(hs(fs)),{schedule:gs,cancel:ys}=K(queueMicrotask,!1),vs={x:!1,y:!1};function ws(){return vs.x||vs.y}function bs(t,e){const n=as(t),s=new AbortController;return[n,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function Ts(t){return!("touch"===t.pointerType||ws())}const xs=(t,e)=>!!e&&(t===e||xs(t,e.parentElement)),Vs=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Ms=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Ss=new WeakSet;function As(t){return e=>{"Enter"===e.key&&t(e)}}function ks(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Es(t){return Vs(t)&&!ws()}function Ps(t,e){const n=window.getComputedStyle(t);return on(e)?n.getPropertyValue(e):n[e]}function Cs(t,e){let n;const s=()=>{const{currentTime:s}=e,i=(null===s?0:s.value)/100;n!==i&&t(i),n=i};return $.preUpdate(s,!0),()=>z(s)}function Os(){const{value:t}=j;null!==t?(t.frameloop.rate.push(U.delta),t.animations.mainThread.push(G.mainThread),t.animations.waapi.push(G.waapi),t.animations.layout.push(G.layout)):z(Os)}function Fs(t){return t.reduce(((t,e)=>t+e),0)/t.length}function Rs(t,e=Fs){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const Bs=t=>Math.round(1e3/t);function Ds(){j.value=null,j.addProjectionMetrics=null}function Ls(){const{value:t}=j;if(!t)throw new Error("Stats are not being measured");Ds(),z(Os);const e={frameloop:{setup:Rs(t.frameloop.setup),rate:Rs(t.frameloop.rate),read:Rs(t.frameloop.read),resolveKeyframes:Rs(t.frameloop.resolveKeyframes),preUpdate:Rs(t.frameloop.preUpdate),update:Rs(t.frameloop.update),preRender:Rs(t.frameloop.preRender),render:Rs(t.frameloop.render),postRender:Rs(t.frameloop.postRender)},animations:{mainThread:Rs(t.animations.mainThread),waapi:Rs(t.animations.waapi),layout:Rs(t.animations.layout)},layoutProjection:{nodes:Rs(t.layoutProjection.nodes),calculatedTargetDeltas:Rs(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:Rs(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=Bs(n.min),n.max=Bs(n.max),n.avg=Bs(n.avg),[n.min,n.max]=[n.max,n.min],e}function Is(t){return a(t)&&"ownerSVGElement"in t}function Ws(t){return Is(t)&&"svg"===t.tagName}function Ns(...t){const e=!Array.isArray(t[0]),n=e?0:-1,s=t[0+n],i=be(t[1+n],t[2+n],t[3+n]);return e?i(s):i}function js(t){const e=[];is.current=e;const n=t();is.current=void 0;const s=os(n);return function(t,e,n){const s=()=>e.set(n()),i=()=>$.preRender(s,!1,!0),r=t.map((t=>t.on("change",i)));e.on("destroy",(()=>{r.forEach((t=>t())),z(s)}))}(e,s,t),s}const Ks=t=>Boolean(t&&t.getVelocity);function $s(t,e,n){const s=t.get();let i,r=null,o=s;const a="string"==typeof s?s.replace(/[\d.-]/g,""):void 0,l=()=>{r&&(r.stop(),r=null)},u=()=>{l(),r=new Fe({keyframes:[Us(t.get()),Us(o)],velocity:t.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:i})};let c;return t.attach(((e,n)=>(o=e,i=t=>n(zs(t,a)),$.postRender(u),t.get())),l),Ks(e)&&(c=e.on("change",(e=>t.set(zs(e,a)))),t.on("destroy",c)),c}function zs(t,e){return e?t+e:t}function Us(t){return"number"==typeof t?t:parseFloat(t)}const Ys=[...jn,bt,Pt],Xs=t=>Ys.find(Nn(t));function Hs(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let qs={},Gs=null;const Zs=(t,e)=>{qs[t]=e},_s=()=>{Gs||(Gs=document.createElement("style"),Gs.id="motion-view");let t="";for(const e in qs){const n=qs[e];t+=`${e} {\n`;for(const[e,s]of Object.entries(n))t+=`  ${e}: ${s};\n`;t+="}\n"}Gs.textContent=t,document.head.appendChild(Gs),qs={}},Js=()=>{Gs&&Gs.parentElement&&Gs.parentElement.removeChild(Gs)};function Qs(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function ti(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}const ei=["layout","enter","exit","new","old"];function ni(t){const{update:e,targets:n,options:s}=t;if(!document.startViewTransition)return new Promise((async t=>{await e(),t(new En([]))}));(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",n)||Zs(":root",{"view-transition-name":"none"}),Zs("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),_s();const i=document.startViewTransition((async()=>{await e()}));return i.finished.finally((()=>{Js()})),new Promise((t=>{i.ready.then((()=>{const e=document.getAnimations().filter(ti),i=[];n.forEach(((t,e)=>{for(const n of ei){if(!t[n])continue;const{keyframes:r,options:o}=t[n];for(let[t,a]of Object.entries(r)){if(!a)continue;const r={...In(s,t),...In(o,t)},l=Hs(n);if("opacity"===t&&!Array.isArray(a)){a=["new"===l?0:1,a]}"function"==typeof r.delay&&(r.delay=r.delay(0,1)),r.duration&&(r.duration=m(r.duration)),r.delay&&(r.delay=m(r.delay));const u=new vn({...r,element:document.documentElement,name:t,pseudoElement:`::view-transition-${l}(${e})`,keyframes:a});i.push(u)}}}));for(const t of e){if("finished"===t.playState)continue;const{effect:e}=t;if(!(e&&e instanceof KeyframeEffect))continue;const{pseudoElement:r}=e;if(!r)continue;const o=Qs(r);if(!o)continue;const a=n.get(o.layer);if(a)si(a,"enter")&&si(a,"exit")&&e.getKeyframes().some((t=>t.mixBlendMode))?i.push(new Cn(t)):t.cancel();else{const n="group"===o.type?"layout":"";let r={...In(s,n)};r.duration&&(r.duration=m(r.duration)),r=yn(r);const a=fn(r.ease,r.duration);e.updateTiming({delay:m(r.delay??0),duration:r.duration,easing:a}),i.push(new Cn(t))}}t(new En(i))}))}))}function si(t,e){return t?.[e]?.keyframes.opacity}let ii=[],ri=null;function oi(){ri=null;const[t]=ii;var e;t&&(n(ii,e=t),ri=e,ni(e).then((t=>{e.notifyReady(t),t.finished.finally(oi)})))}function ai(){for(let t=ii.length-1;t>=0;t--){const e=ii[t],{interrupt:n}=e.options;if("immediate"===n){const n=ii.slice(0,t+1).map((t=>t.update)),s=ii.slice(t+1);e.update=()=>{n.forEach((t=>t()))},ii=[e,...s];break}}ri&&"immediate"!==ii[0]?.options.interrupt||oi()}class li{constructor(t,e={}){var n;this.currentTarget="root",this.targets=new Map,this.notifyReady=c,this.readyPromise=new Promise((t=>{this.notifyReady=t})),this.update=t,this.options={interrupt:"wait",...e},n=this,ii.push(n),gs.render(ai)}get(t){return this.currentTarget=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentTarget:s,targets:i}=this;i.has(s)||i.set(s,{});i.get(s)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const ui=$,ci=N.reduce(((t,e)=>(t[e]=t=>z(t),t)),{});function hi(t){return"object"==typeof t&&!Array.isArray(t)}function di(t,e,n,s){return"string"==typeof t&&hi(e)?as(t,n,s):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function pi(t,e,n){return t*(e+1)}function fi(t,e,n,s){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:s.get(e)??t}function mi(t,e,s,i,r,o){!function(t,e,s){for(let i=0;i<t.length;i++){const r=t[i];r.at>e&&r.at<s&&(n(t,r),i--)}}(t,r,o);for(let n=0;n<e.length;n++)t.push({value:e[n],at:Rt(r,o,i[n]),easing:D(s,n)})}function gi(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function yi(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function vi(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function wi(t,e){return e[t]||(e[t]=[]),e[t]}function bi(t){return Array.isArray(t)?t:[t]}function Ti(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const xi=t=>"number"==typeof t,Vi=t=>t.every(xi),Mi=new WeakMap;function Si(t){const e=[{},{}];return t?.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function Ai(t,e,n,s){if("function"==typeof e){const[i,r]=Si(s);e=e(void 0!==n?n:t.custom,i,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[i,r]=Si(s);e=e(void 0!==n?n:t.custom,i,r)}return e}function ki(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,os(n))}function Ei(t){return(t=>Array.isArray(t))(t)?t[t.length-1]||0:t}function Pi(t,e){const n=function(t,e,n){const s=t.getProps();return Ai(s,e,void 0!==n?n:s.custom,t)}(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const e in r){ki(t,e,Ei(r[e]))}}function Ci(t,e){const n=t.getValue("willChange");if(s=n,Boolean(Ks(s)&&s.add))return n.add(e);if(!n&&r.WillChange){const n=new r.WillChange("auto");t.addValue("willChange",n),n.add(e)}var s}const Oi=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Fi="data-"+Oi("framerAppearId");function Ri(t){return t.props[Fi]}const Bi=t=>null!==t;const Di={type:"spring",stiffness:500,damping:25,restSpeed:10},Li={type:"keyframes",duration:.8},Ii={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Wi=(t,{keyframes:e})=>e.length>2?Li:Xe.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Di:Ii;const Ni=(t,e,n,s={},i,o)=>a=>{const l=In(s,t)||{},u=l.delay||s.delay||0;let{elapsed:c=0}=s;c-=m(u);const h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-c,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:o?void 0:i};(function({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(l)||Object.assign(h,Wi(t,h)),h.duration&&(h.duration=m(h.duration)),h.repeatDelay&&(h.repeatDelay=m(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(h.duration=0,0===h.delay&&(d=!0)),(r.instantAnimations||r.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),h.allowFlatten=!l.type&&!l.ease,d&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(Bi),r=e&&"loop"!==n&&e%2==1?0:i.length-1;return r&&void 0!==s?s:i[r]}(h.keyframes,l);if(void 0!==t)return void $.update((()=>{h.onUpdate(t),h.onComplete()}))}return l.isSync?new Fe(h):new kn(h)};function ji({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,s}function Ki(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(r=s);const l=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const e in a){const s=t.getValue(e,t.latestValues[e]??null),i=a[e];if(void 0===i||u&&ji(u,e))continue;const o={delay:n,...In(r||{},e)},c=s.get();if(void 0!==c&&!s.isAnimating&&!Array.isArray(i)&&i===c&&!o.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){const n=Ri(t);if(n){const t=window.MotionHandoffAnimation(n,e,$);null!==t&&(o.startTime=t,h=!0)}}Ci(t,e),s.start(Ni(e,s,i,t.shouldReduceMotion&&Wn.has(e)?{type:!1}:o,t,h));const d=s.animation;d&&l.push(d)}return o&&Promise.all(l).then((()=>{$.update((()=>{o&&Pi(t,o)}))})),l}const $i={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},zi={};for(const t in $i)zi[t]={isEnabled:e=>$i[t].some((t=>!!e[t]))};const Ui=()=>({x:{min:0,max:0},y:{min:0,max:0}}),Yi="undefined"!=typeof window,Xi={current:null},Hi={current:!1};const qi=["initial","animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"];function Gi(t){return null!==(e=t.animate)&&"object"==typeof e&&"function"==typeof e.start||qi.some((e=>function(t){return"string"==typeof t||Array.isArray(t)}(t[e])));var e}const Zi=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class _i{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:i,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=rn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=q.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,$.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=Boolean(i),this.isControllingVariants=Gi(e),this.isVariantNode=function(t){return Boolean(Gi(t)||t.variants)}(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in c){const e=c[t];void 0!==a[t]&&Ks(e)&&e.set(a[t],!1)}}mount(t){this.current=t,Mi.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),Hi.current||function(){if(Hi.current=!0,Yi)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Xi.current=t.matches;t.addListener(e),e()}else Xi.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Xi.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),z(this.notifyUpdate),z(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Xe.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&$.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),i=e.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{s(),i(),r&&r(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in zi){const e=zi[t];if(!e)continue;const{isEnabled:n,Feature:s}=e;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Zi.length;e++){const n=Zi[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const s=t["on"+n];s&&(this.propEventSubscriptions[n]=this.on(n,s))}this.prevMotionValues=function(t,e,n){for(const s in e){const i=e[s],r=n[s];if(Ks(i))t.addValue(s,i);else if(Ks(r))t.addValue(s,os(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{const e=t.getStaticValue(s);t.addValue(s,os(void 0!==e?e:i,{owner:t}))}}for(const s in n)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=os(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(o(n)||l(n))?n=parseFloat(n):!Xs(n)&&Pt.test(e)&&(n=_n(t,e)),this.setBaseTarget(t,Ks(n)?n.get():n)),Ks(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const s=Ai(this.props,e,this.presenceContext?.custom);s&&(n=s[t])}if(e&&void 0!==n)return n;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||Ks(s)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new f),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Ji extends _i{constructor(){super(...arguments),this.KeyframeResolver=Qn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Ks(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}const Qi={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tr=Ye.length;function er(t,e,n){const{style:s,vars:i,transformOrigin:r}=t;let o=!1,a=!1;for(const t in e){const n=e[t];if(Xe.has(t))o=!0;else if(_(t))i[t]=n;else{const e=us(n,qn[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(e.transform||(o||n?s.transform=function(t,e,n){let s="",i=!0;for(let r=0;r<tr;r++){const o=Ye[r],a=t[o];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=us(a,qn[o]);l||(i=!1,s+=`${Qi[o]||o}(${t}) `),n&&(e[o]=t)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=r;s.transformOrigin=`${t} ${e} ${n}`}}function nr(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const e in n)t.style.setProperty(e,n[e])}const sr={};function ir(t,{layout:e,layoutId:n}){return Xe.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!sr[t]||"opacity"===t)}function rr(t,e,n){const{style:s}=t,i={};for(const r in s)(Ks(s[r])||e.style&&Ks(e.style[r])||ir(r,t)||void 0!==n?.getValue(r)?.liveStyle)&&(i[r]=s[r]);return i}class or extends Ji{constructor(){super(...arguments),this.type="html",this.renderInstance=nr}readValueFromInstance(t,e){if(Xe.has(e))return this.projection?.isProjecting?Ke(e):ze(t,e);{const s=(n=t,window.getComputedStyle(n)),i=(_(e)?s.getPropertyValue(e):s[e])||0;return"string"==typeof i?i.trim():i}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return function(t,e){return function({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}(t,e)}build(t,e,n){er(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return rr(t,e,n)}}class ar extends _i{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}const lr={offset:"stroke-dashoffset",array:"stroke-dasharray"},ur={offset:"strokeDashoffset",array:"strokeDasharray"};function cr(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:r=1,pathOffset:o=0,...a},l,u,c){if(er(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==n&&(h.y=n),void 0!==s&&(h.scale=s),void 0!==i&&function(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?lr:ur;t[r.offset]=mt.transform(-s);const o=mt.transform(e),a=mt.transform(n);t[r.array]=`${o} ${a}`}(h,i,r,o,!1)}const hr=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class dr extends Ji{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ui}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Xe.has(e)){const t=Zn(e);return t&&t.default||0}return e=hr.has(e)?e:Oi(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return function(t,e,n){const s=rr(t,e,n);for(const n in t)(Ks(t[n])||Ks(e[n]))&&(s[-1!==Ye.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]);return s}(t,e,n)}build(t,e,n){cr(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,s){!function(t,e,n,s){nr(t,e,void 0,s);for(const n in e.attrs)t.setAttribute(hr.has(n)?n:Oi(n),e.attrs[n])}(t,e,0,s)}mount(t){var e;this.isSVGTag="string"==typeof(e=t.tagName)&&"svg"===e.toLowerCase(),super.mount(t)}}function pr(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=Is(t)&&!Ws(t)?new dr(e):new or(e);n.mount(t),Mi.set(t,n)}function fr(t){const e=new ar({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),Mi.set(t,e)}function mr(t,e,n,s){const i=[];if(function(t,e){return Ks(t)||"number"==typeof t||"string"==typeof t&&!hi(e)}(t,e))i.push(function(t,e,n){const s=Ks(t)?t:os(t);return s.start(Ni("",s,e,n)),s.animation}(t,hi(e)&&e.default||e,n&&n.default||n));else{const r=di(t,e,s),o=r.length;for(let t=0;t<o;t++){const s=r[t],a=s instanceof Element?pr:fr;Mi.has(s)||a(s);const l=Mi.get(s),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,o)),i.push(...Ki(l,{...e,transition:u},{}))}}return i}function gr(t,e,n){const s=[],i=function(t,{defaultTransition:e={},...n}={},s,i){const r=e.duration||.3,o=new Map,a=new Map,l={},u=new Map;let c=0,h=0,d=0;for(let n=0;n<t.length;n++){const o=t[n];if("string"==typeof o){u.set(o,h);continue}if(!Array.isArray(o)){u.set(o.name,fi(h,o.at,c,u));continue}let[p,f,g={}]=o;void 0!==g.at&&(h=fi(h,g.at,c,u));let y=0;const v=(t,n,s,o=0,a=0)=>{const l=bi(t),{delay:u=0,times:c=xe(l),type:p="keyframes",repeat:f,repeatType:g,repeatDelay:v=0,...w}=n;let{ease:b=e.ease||"easeOut",duration:T}=n;const x="function"==typeof u?u(o,a):u,V=l.length,M=gn(p)?p:i?.[p];if(V<=2&&M){let t=100;if(2===V&&Vi(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...w};void 0!==T&&(e.duration=m(T));const n=Zt(e,t,M);b=n.ease,T=n.duration}T??(T=r);const S=h+x;1===c.length&&0===c[0]&&(c[1]=1);const A=c.length-l.length;if(A>0&&Te(c,A),1===l.length&&l.unshift(null),f){T=pi(T,f);const t=[...l],e=[...c];b=Array.isArray(b)?[...b]:[b];const n=[...b];for(let s=0;s<f;s++){l.push(...t);for(let i=0;i<t.length;i++)c.push(e[i]+(s+1)),b.push(0===i?"linear":D(n,i-1))}gi(c,f)}const k=S+T;mi(s,l,b,c,S,k),y=Math.max(x+T,y),d=Math.max(k,d)};if(Ks(p))v(f,g,wi("default",vi(p,a)));else{const t=di(p,f,s,l),e=t.length;for(let n=0;n<e;n++){const s=vi(t[n],a);for(const t in f)v(f[t],Ti(g,t),wi(t,s),n,e)}}c=h,h+=y}return a.forEach(((t,s)=>{for(const i in t){const r=t[i];r.sort(yi);const a=[],l=[],u=[];for(let t=0;t<r.length;t++){const{at:e,value:n,easing:s}=r[t];a.push(n),l.push(p(0,d,e)),u.push(s||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),o.has(s)||o.set(s,{keyframes:{},transition:{}});const c=o.get(s);c.keyframes[i]=a,c.transition[i]={...e,duration:d,ease:u,times:l,...n}}})),o}(t,e,n,{spring:ve});return i.forEach((({keyframes:t,transition:e},n)=>{s.push(...mr(n,t,e))})),s}function yr(t){return function(e,n,s){let i=[];var r;r=e,i=Array.isArray(r)&&r.some(Array.isArray)?gr(e,n,t):mr(e,n,s,t);const o=new Pn(i);return t&&t.animations.push(o),o}}const vr=yr();const wr=t=>function(e,n,s){return new Pn(function(t,e,n,s){const i=as(t,s),r=i.length,o=[];for(let t=0;t<r;t++){const s=i[t],a={...n};"function"==typeof a.delay&&(a.delay=a.delay(t,r));for(const t in e){let n=e[t];Array.isArray(n)||(n=[n]);const i={...In(a,t)};i.duration&&(i.duration=m(i.duration)),i.delay&&(i.delay=m(i.delay));const r=Rn(s),l=Fn(t,i.pseudoElement||""),u=r.get(l);u&&u.stop(),o.push({map:r,key:l,unresolvedKeyframes:n,options:{...i,element:s,name:t,allowFlatten:!a.type&&!a.ease}})}}for(let t=0;t<o.length;t++){const{unresolvedKeyframes:e,options:n}=o[t],{element:s,name:i,pseudoElement:r}=n;r||null!==e[0]||(e[0]=Ps(s,i)),Re(e),es(e,i),!r&&e.length<2&&e.unshift(Ps(s,i)),n.keyframes=e}const a=[];for(let t=0;t<o.length;t++){const{map:e,key:n,options:s}=o[t],i=new vn(s);e.set(n,i),i.finished.finally((()=>e.delete(n))),a.push(i)}return a}(e,n,s,t))},br=wr(),Tr=new WeakMap;let xr;function Vr({target:t,contentRect:e,borderBoxSize:n}){Tr.get(t)?.forEach((s=>{s({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return Is(t)&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})}))}function Mr(t){t.forEach(Vr)}function Sr(t,e){xr||"undefined"!=typeof ResizeObserver&&(xr=new ResizeObserver(Mr));const n=as(t);return n.forEach((t=>{let n=Tr.get(t);n||(n=new Set,Tr.set(t,n)),n.add(e),xr?.observe(t)})),()=>{n.forEach((t=>{const n=Tr.get(t);n?.delete(e),n?.size||xr?.unobserve(t)}))}}const Ar=new Set;let kr;function Er(t){return Ar.add(t),kr||(kr=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Ar.forEach((t=>t(e)))},window.addEventListener("resize",kr)),()=>{Ar.delete(t),!Ar.size&&kr&&(kr=void 0)}}const Pr={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Cr(t,e,n,s){const i=n[e],{length:r,position:o}=Pr[e],a=i.current,l=n.time;i.current=t[`scroll${o}`],i.scrollLength=t[`scroll${r}`]-t[`client${r}`],i.offset.length=0,i.offset[0]=0,i.offset[1]=i.scrollLength,i.progress=p(0,i.scrollLength,i.current);const u=s-l;i.velocity=u>50?0:y(i.current-a,u)}const Or={start:0,center:.5,end:1};function Fr(t,e,n=0){let s=0;if(t in Or&&(t=Or[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?s=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?s=e/100*document.documentElement.clientWidth:t.endsWith("vh")?s=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(s=e*t),n+s}const Rr=[0,0];function Br(t,e,n,s){let i=Array.isArray(t)?t:Rr,r=0,o=0;return"number"==typeof t?i=[t,t]:"string"==typeof t&&(i=(t=t.trim()).includes(" ")?t.split(" "):[t,Or[t]?t:"0"]),r=Fr(i[0],n,s),o=Fr(i[1],e),r-o}const Dr={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Lr={x:0,y:0};function Ir(t,e,n){const{offset:i=Dr.All}=n,{target:r=t,axis:o="y"}=n,a="y"===o?"height":"width",l=r!==t?function(t,e){const n={x:0,y:0};let s=t;for(;s&&s!==e;)if(Vn(s))n.x+=s.offsetLeft,n.y+=s.offsetTop,s=s.offsetParent;else if("svg"===s.tagName){const t=s.getBoundingClientRect();s=s.parentElement;const e=s.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(s instanceof SVGGraphicsElement))break;{const{x:t,y:e}=s.getBBox();n.x+=t,n.y+=e;let i=null,r=s.parentNode;for(;!i;)"svg"===r.tagName&&(i=r),r=s.parentNode;s=i}}return n}(r,t):Lr,u=r===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(r),c={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let h=!e[o].interpolate;const d=i.length;for(let t=0;t<d;t++){const n=Br(i[t],c[a],u[a],l[o]);h||n===e[o].interpolatorOffsets[t]||(h=!0),e[o].offset[t]=n}h&&(e[o].interpolate=be(e[o].offset,xe(i),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=s(0,1,e[o].interpolate(e[o].current))}function Wr(t,e,n,s={}){return{measure:e=>{!function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let s=e;for(;s&&s!==t;)n.x.targetOffset+=s.offsetLeft,n.y.targetOffset+=s.offsetTop,s=s.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,s.target,n),function(t,e,n){Cr(t,"x",e,n),Cr(t,"y",e,n),e.time=n}(t,n,e),(s.offset||s.target)&&Ir(t,n,s)},notify:()=>e(n)}}const Nr=new WeakMap,jr=new WeakMap,Kr=new WeakMap,$r=t=>t===document.scrollingElement?window:t;function zr(t,{container:e=document.scrollingElement,...n}={}){if(!e)return c;let s=Kr.get(e);s||(s=new Set,Kr.set(e,s));const i=Wr(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(s.add(i),!Nr.has(e)){const t=()=>{for(const t of s)t.measure(U.timestamp);$.preUpdate(n)},n=()=>{for(const t of s)t.notify()},i=()=>$.read(t);Nr.set(e,i);const a=$r(e);window.addEventListener("resize",i,{passive:!0}),e!==document.documentElement&&jr.set(e,(o=i,"function"==typeof(r=e)?Er(r):Sr(r,o))),a.addEventListener("scroll",i,{passive:!0}),i()}var r,o;const a=Nr.get(e);return $.read(a,!1,!0),()=>{z(a);const t=Kr.get(e);if(!t)return;if(t.delete(i),t.size)return;const n=Nr.get(e);Nr.delete(e),n&&($r(e).removeEventListener("scroll",n),jr.get(e)?.(),window.removeEventListener("resize",n))}}const Ur=new Map;function Yr({source:t,container:e,...n}){const{axis:s}=n;t&&(e=t);const i=Ur.get(e)??new Map;Ur.set(e,i);const r=n.target??"self",o=i.get(r)??{},a=s+(n.offset??[]).join(",");return o[a]||(o[a]=!n.target&&ln()?new ScrollTimeline({source:e,axis:s}):function(t){const e={value:0},n=zr((n=>{e.value=100*n[t.axis].progress}),t);return{currentTime:e,cancel:n}}({container:e,...n})),o[a]}const Xr={some:0,all:1};const Hr=(t,e)=>Math.abs(t-e);t.AsyncMotionValueAnimation=kn,t.DOMKeyframesResolver=Qn,t.GroupAnimation=En,t.GroupAnimationWithThen=Pn,t.JSAnimation=Fe,t.KeyframeResolver=rn,t.MotionGlobalConfig=r,t.MotionValue=rs,t.NativeAnimation=vn,t.NativeAnimationExtended=Tn,t.NativeAnimationWrapper=Cn,t.SubscriptionManager=f,t.ViewTransitionBuilder=li,t.acceleratedValues=ss,t.activeAnimations=G,t.addStyleValue=fs,t.addUniqueItem=e,t.alpha=nt,t.analyseComplexValue=St,t.animate=vr,t.animateMini=br,t.animateValue=function(t){return new Fe(t)},t.animateView=function(t,e={}){return new li(t,e)},t.animationMapKey=Fn,t.anticipate=k,t.applyPxDefaults=es,t.attachSpring=$s,t.backIn=S,t.backInOut=A,t.backOut=M,t.calcGeneratorDuration=Gt,t.cancelFrame=z,t.cancelMicrotask=ys,t.cancelSync=ci,t.circIn=E,t.circInOut=C,t.circOut=P,t.clamp=s,t.collectMotionValues=is,t.color=bt,t.complex=Pt,t.convertOffsetToTimes=Ve,t.createGeneratorEasing=Zt,t.createRenderBatcher=K,t.createScopedAnimate=yr,t.cubicBezier=T,t.cubicBezierAsString=dn,t.defaultEasing=Me,t.defaultOffset=xe,t.defaultTransformValue=Ke,t.defaultValueTypes=Gn,t.degrees=pt,t.delay=function(t,e){return function(t,e){const n=q.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&(z(s),t(r-e))};return $.setup(s,!0),()=>z(s)}(t,m(e))},t.dimensionValueTypes=jn,t.distance=Hr,t.distance2D=function(t,e){const n=Hr(t.x,e.x),s=Hr(t.y,e.y);return Math.sqrt(n**2+s**2)},t.easeIn=O,t.easeInOut=R,t.easeOut=F,t.easingDefinitionToFunction=W,t.fillOffset=Te,t.fillWildcards=Re,t.findDimensionValueType=Kn,t.findValueType=Xs,t.flushKeyframeResolvers=sn,t.frame=$,t.frameData=U,t.frameSteps=Y,t.generateLinearEasing=Ht,t.getAnimatableNone=_n,t.getAnimationMap=Rn,t.getComputedStyle=Ps,t.getDefaultValueType=Zn,t.getEasingForSegment=D,t.getMixer=Kt,t.getValueAsType=us,t.getValueTransition=In,t.getVariableValue=Ln,t.hasWarned=function(t){return v.has(t)},t.hex=ht,t.hover=function(t,e,n={}){const[s,i,r]=bs(t,n),o=t=>{if(!Ts(t))return;const{target:n}=t,s=e(n,t);if("function"!=typeof s||!n)return;const r=t=>{Ts(t)&&(s(t),n.removeEventListener("pointerleave",r))};n.addEventListener("pointerleave",r,i)};return s.forEach((t=>{t.addEventListener("pointerenter",o,i)})),r},t.hsla=wt,t.hslaToRgba=Ot,t.inView=function(t,e,{root:n,margin:s,amount:i="some"}={}){const r=as(t),o=new WeakMap,a=new IntersectionObserver((t=>{t.forEach((t=>{const n=o.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t.target,t);"function"==typeof n?o.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),o.delete(t.target))}))}),{root:n,rootMargin:s,threshold:"number"==typeof i?i:Xr[i]});return r.forEach((t=>a.observe(t))),()=>a.disconnect()},t.inertia=we,t.interpolate=be,t.invariant=i,t.invisibleValues=Wt,t.isBezierDefinition=L,t.isCSSVariableName=_,t.isCSSVariableToken=Q,t.isDragActive=ws,t.isDragging=vs,t.isEasingArray=B,t.isGenerator=gn,t.isHTMLElement=Vn,t.isMotionValue=Ks,t.isNodeOrChild=xs,t.isNumericalString=o,t.isObject=a,t.isPrimaryPointer=Vs,t.isSVGElement=Is,t.isSVGSVGElement=Ws,t.isWaapiSupportedEasing=function t(e){return Boolean("function"==typeof e&&hn()||!e||"string"==typeof e&&(e in pn||hn())||L(e)||Array.isArray(e)&&e.every(t))},t.isZeroValueString=l,t.keyframes=Se,t.mapEasingToNativeEasing=fn,t.mapValue=function(t,e,n,s){const i=Ns(e,n,s);return js((()=>i(t.get())))},t.maxGeneratorDuration=qt,t.memo=u,t.microtask=gs,t.millisecondsToSeconds=g,t.mirrorEasing=x,t.mix=Yt,t.mixArray=$t,t.mixColor=It,t.mixComplex=Ut,t.mixImmediate=Ft,t.mixLinearColor=Bt,t.mixNumber=Rt,t.mixObject=zt,t.mixVisibility=Nt,t.motionValue=os,t.moveItem=function([...t],e,n){const s=e<0?t.length+e:e;if(s>=0&&s<t.length){const s=n<0?t.length+n:n,[i]=t.splice(e,1);t.splice(s,0,i)}return t},t.noop=c,t.number=et,t.numberValueTypes=qn,t.observeTimeline=Cs,t.parseCSSVariable=Dn,t.parseValueFromTransform=$e,t.percent=ft,t.pipe=d,t.positionalKeys=Wn,t.press=function(t,e,n={}){const[s,i,r]=bs(t,n),o=t=>{const s=t.currentTarget;if(!Es(t))return;Ss.add(s);const r=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Ss.has(s)&&Ss.delete(s),Es(t)&&"function"==typeof r&&r(t,{success:e})},a=t=>{o(t,s===window||s===document||n.useGlobalTarget||xs(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return s.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",o,i),Vn(t)&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const s=As((()=>{if(Ss.has(n))return;ks(n,"down");const t=As((()=>{ks(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>ks(n,"cancel")),e)}));n.addEventListener("keydown",s,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",s)),e)})(t,i))),e=t,Ms.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),r},t.progress=p,t.progressPercentage=vt,t.px=mt,t.readTransformValue=ze,t.recordStats=function(){if(j.value)throw Ds(),new Error("Stats are already being measured");const t=j;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},$.postRender(Os,!0),Ls},t.removeItem=n,t.resolveElements=as,t.reverseEasing=V,t.rgbUnit=ut,t.rgba=ct,t.scale=st,t.scroll=function(t,{axis:e="y",container:n=document.scrollingElement,...s}={}){if(!n)return c;const i={axis:e,container:n,...s};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)?zr((n=>{t(n[e.axis].progress,n)}),e):Cs(t,Yr(e))}(t,i):function(t,e){const n=Yr(e);return t.attachTimeline({timeline:e.target?void 0:n,observe:t=>(t.pause(),Cs((e=>{t.time=t.duration*e}),n))})}(t,i)},t.scrollInfo=zr,t.secondsToMilliseconds=m,t.setDragLock=function(t){return"x"===t||"y"===t?vs[t]?null:(vs[t]=!0,()=>{vs[t]=!1}):vs.x||vs.y?null:(vs.x=vs.y=!0,()=>{vs.x=vs.y=!1})},t.setStyle=an,t.spring=ve,t.springValue=function(t,e){const n=os(Ks(t)?t.get():t);return $s(n,t,e),n},t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:s}={}){return(i,r)=>{const o="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,r),a=Math.abs(o-i);let l=t*a;if(s){const e=r*t;l=W(s)(l/e)*e}return e+l}},t.startWaapiAnimation=mn,t.statsBuffer=j,t.steps=function(t,e="end"){return n=>{const i=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,r="end"===e?Math.floor(i):Math.ceil(i);return s(0,1,r/t)}},t.styleEffect=ms,t.supportedWaapiEasing=pn,t.supportsBrowserAnimation=An,t.supportsFlags=un,t.supportsLinearEasing=hn,t.supportsPartialKeyframes=ns,t.supportsScrollTimeline=ln,t.sync=ui,t.testValueType=Nn,t.time=q,t.transform=Ns,t.transformPropOrder=Ye,t.transformProps=Xe,t.transformValue=js,t.transformValueTypes=Hn,t.velocityPerSecond=y,t.vh=gt,t.vw=yt,t.warnOnce=function(t,e,n){t||v.has(e)||(console.warn(e),n&&console.warn(n),v.add(e))},t.warning=()=>{},t.wrap=w}));
