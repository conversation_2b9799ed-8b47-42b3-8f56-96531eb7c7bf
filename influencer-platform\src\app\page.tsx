'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import { useRouter } from 'next/navigation'
import {
  Shield,
  Star,
  Users,
  TrendingUp,
  CheckCircle,
  ArrowLeft
} from 'lucide-react'

export default function Home() {
  const { isAuthenticated, user } = useAuth()
  const router = useRouter()

  const features = [
    {
      icon: Shield,
      title: 'أمان وموثوقية',
      description: 'نضمن حقوقك والأموال محجوزة حتى إتمام الإعلان'
    },
    {
      icon: Star,
      title: 'مؤثرين معتمدين',
      description: 'مؤثرين ومبدعين محتوى معتمدين ومتنوعين'
    },
    {
      icon: Users,
      title: 'جمهور سعودي',
      description: 'استهدف الجمهور السعودي بدقة عالية'
    },
    {
      icon: TrendingUp,
      title: 'نتائج مضمونة',
      description: 'تتبع النتائج وقياس الأداء بشكل مباشر'
    }
  ]

  const stats = [
    { number: '500+', label: 'مؤثر ومبدع' },
    { number: '1000+', label: 'حملة مكتملة' },
    { number: '95%', label: 'رضا العملاء' },
    { number: '24/7', label: 'دعم فني' }
  ]

  // Redirect authenticated users to their dashboard
  React.useEffect(() => {
    if (isAuthenticated && user) {
      switch (user.type) {
        case 'merchant':
          router.push('/merchant/dashboard')
          break
        case 'influencer':
        case 'ugc_creator':
          router.push('/creator/dashboard')
          break
        case 'admin':
          router.push('/admin/dashboard')
          break
      }
    }
  }, [isAuthenticated, user, router])

  if (isAuthenticated && user) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }

  return (
    <MobileLayout showBottomNav={false}>
      <div className="min-h-screen">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-slate-900 to-slate-800 px-6 py-16 lg:py-24">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
            <div className="mb-6">
              <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-3xl">🇸🇦</span>
              </div>
              <h1 className="text-4xl lg:text-6xl font-bold text-white mb-4">
                منصة المؤثرين السعودية
              </h1>
              <p className="text-xl lg:text-2xl text-slate-300 max-w-3xl mx-auto">
                اربط علامتك التجارية مع أفضل المؤثرين والمبدعين في المملكة العربية السعودية
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8 max-w-2xl mx-auto">
              <Button
                variant="primary"
                size="lg"
                onClick={() => router.push('/auth/register?type=merchant')}
                rightIcon={<ArrowLeft className="w-5 h-5" />}
                className="w-full sm:w-auto px-6"
              >
                اطلق إعلانك مع المؤثرين
              </Button>

              <Button
                variant="secondary"
                size="lg"
                onClick={() => router.push('/auth/register?type=creator')}
                className="w-full sm:w-auto px-6"
              >
                سجّل كمؤثر أو مبدع محتوى
              </Button>
            </div>

            <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse text-sm text-slate-400">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
                <span>مجاني التسجيل</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
                <span>دفع آمن</span>
              </div>
            </div>
          </motion.div>
        </section>

        {/* Stats Section */}
        <section className="px-6 py-12 lg:py-16 bg-slate-800">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 lg:gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-2xl font-bold text-green-600 mb-1">
                  {stat.number}
                </div>
                <div className="text-sm text-slate-300">
                  {stat.label}
                </div>
              </motion.div>
            ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="px-6 py-16 lg:py-24 bg-slate-900">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-center text-white mb-12 lg:mb-16">
                لماذا تختار منصتنا؟
              </h2>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
              {features.map((feature, index) => {
                const Icon = feature.icon
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  >
                    <Card className="flex items-start space-x-4 rtl:space-x-reverse">
                      <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Icon className="w-6 h-6 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-white mb-1">
                          {feature.title}
                        </h3>
                        <p className="text-slate-300 text-sm">
                          {feature.description}
                        </p>
                      </div>
                    </Card>
                  </motion.div>
                )
              })}
              </div>
            </motion.div>
          </div>
        </section>

        {/* How it Works Section */}
        <section className="px-6 py-16 lg:py-24 bg-slate-800">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-center text-white mb-12 lg:mb-16">
                كيف تعمل المنصة؟
              </h2>

              <div className="space-y-8 lg:space-y-12">
              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                  1
                </div>
                <div>
                  <h3 className="font-semibold text-white mb-1">
                    اختر المؤثر المناسب
                  </h3>
                  <p className="text-slate-300 text-sm">
                    تصفح قائمة المؤثرين والمبدعين واختر الأنسب لعلامتك التجارية
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                  2
                </div>
                <div>
                  <h3 className="font-semibold text-white mb-1">
                    اطلب الخدمة وادفع
                  </h3>
                  <p className="text-slate-300 text-sm">
                    حدد نوع الخدمة المطلوبة وادفع بأمان عبر Apple Pay أو Google Pay
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                  3
                </div>
                <div>
                  <h3 className="font-semibold text-white mb-1">
                    تابع التنفيذ والنتائج
                  </h3>
                  <p className="text-slate-300 text-sm">
                    تابع تقدم الحملة واحصل على النتائج والتقارير المفصلة
                  </p>
                </div>
              </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="px-6 py-16 lg:py-24 bg-gradient-to-br from-green-500 to-green-600">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="text-center text-white"
            >
              <h2 className="text-3xl lg:text-4xl font-bold mb-6">
                ابدأ رحلتك الآن
              </h2>
              <p className="text-xl text-green-100 mb-10 max-w-2xl mx-auto">
                انضم إلى آلاف التجار والمؤثرين الذين يثقون بمنصتنا
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
                <Button
                  variant="secondary"
                  size="lg"
                  onClick={() => router.push('/auth/register')}
                  className="w-full sm:w-auto px-8"
                >
                  إنشاء حساب مجاني
                </Button>

                <button
                  className="text-green-100 underline text-lg hover:text-white transition-colors"
                  onClick={() => router.push('/auth/login')}
                >
                  لديك حساب بالفعل؟ سجل الدخول
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Footer */}
        <footer className="px-6 py-12 lg:py-16 bg-slate-950 text-white">
          <div className="max-w-6xl mx-auto">
            <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-xl">🇸🇦</span>
            </div>
            <h3 className="font-bold mb-2">منصة المؤثرين السعودية</h3>
            <p className="text-slate-400 text-sm mb-4">
              نربط التجار بأفضل المؤثرين والمبدعين في المملكة
            </p>

            <div className="flex justify-center space-x-6 rtl:space-x-reverse text-sm text-slate-400">
              <button
                onClick={() => router.push('/privacy')}
                className="hover:text-white"
              >
                سياسة الخصوصية
              </button>
              <button
                onClick={() => router.push('/terms')}
                className="hover:text-white"
              >
                الشروط والأحكام
              </button>
              <button
                onClick={() => router.push('/contact')}
                className="hover:text-white"
              >
                اتصل بنا
              </button>
            </div>

            <div className="mt-6 pt-6 border-t border-slate-800 text-xs text-slate-500">
              © 2024 منصة المؤثرين السعودية. جميع الحقوق محفوظة.
            </div>
            </div>
          </div>
        </footer>
      </div>
    </MobileLayout>
  )
}
