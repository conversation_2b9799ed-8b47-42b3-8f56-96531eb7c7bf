# Environment Configuration
NODE_ENV=development

# App Configuration
NEXT_PUBLIC_APP_NAME="منصة المؤثرين السعودية"
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/influencer_platform"
DATABASE_DIRECT_URL="postgresql://username:password@localhost:5432/influencer_platform"

# Authentication
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# OAuth Providers (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# File Storage (AWS S3 or similar)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name

# Payment Processing
STRIPE_PUBLIC_KEY=pk_test_your-stripe-public-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# SMS Service (for Saudi Arabia)
SMS_API_KEY=your-sms-api-key
SMS_SENDER_ID=your-sender-id

# Analytics
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Error Tracking
SENTRY_DSN=your-sentry-dsn

# Rate Limiting
UPSTASH_REDIS_REST_URL=your-redis-url
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_CHAT=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true

# Saudi Arabia Specific
NEXT_PUBLIC_VAT_RATE=0.15
NEXT_PUBLIC_PLATFORM_FEE=0.05
NEXT_PUBLIC_CURRENCY=SAR
NEXT_PUBLIC_LOCALE=ar-SA
