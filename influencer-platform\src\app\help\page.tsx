'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  ArrowLeft,
  Search,
  HelpCircle,
  MessageCircle,
  Phone,
  Mail,
  ChevronDown,
  ChevronUp,
  Book,
  Video,
  FileText,
  Users,
  CreditCard,
  Shield,
  Star,
  Zap
} from 'lucide-react'

export default function HelpPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null)

  const helpCategories = [
    {
      icon: Users,
      title: 'البدء مع المنصة',
      description: 'كيفية إنشاء حساب والبدء',
      color: 'bg-blue-100 text-blue-600'
    },
    {
      icon: CreditCard,
      title: 'المدفوعات والفواتير',
      description: 'كل ما يتعلق بالدفع والاستلام',
      color: 'bg-green-100 text-green-600'
    },
    {
      icon: Shield,
      title: 'الأمان والخصوصية',
      description: 'حماية حسابك وبياناتك',
      color: 'bg-purple-100 text-purple-600'
    },
    {
      icon: Star,
      title: 'إدارة الحملات',
      description: 'إنشاء وإدارة الحملات الإعلانية',
      color: 'bg-orange-100 text-orange-600'
    }
  ]

  const faqs = [
    {
      question: 'كيف يمكنني إنشاء حساب جديد؟',
      answer: 'يمكنك إنشاء حساب جديد بالضغط على "إنشاء حساب" واختيار نوع المستخدم (تاجر أو مؤثر) ثم ملء البيانات المطلوبة. ستحتاج إلى تأكيد البريد الإلكتروني ورقم الجوال.'
    },
    {
      question: 'كيف تعمل عملية الدفع؟',
      answer: 'نستخدم نظام دفع آمن يدعم Apple Pay و Google Pay. الأموال تُحجز عند إنشاء الطلب وتُحرر للمؤثر بعد إتمام الخدمة بنجاح. نحن نتقاضى عمولة صغيرة من كل معاملة.'
    },
    {
      question: 'ما هي شروط أن أصبح مؤثراً معتمداً؟',
      answer: 'للحصول على التوثيق، يجب أن يكون لديك 1000 متابع على الأقل في إحدى المنصات، ملف شخصي مكتمل، ومعدل تفاعل جيد. كما نراجع جودة المحتوى والالتزام بالقوانين السعودية.'
    },
    {
      question: 'كيف يمكنني إلغاء طلب؟',
      answer: 'يمكن إلغاء الطلبات قبل موافقة المؤثر مجاناً. بعد الموافقة، يتطلب الإلغاء موافقة الطرفين أو تدخل فريق الدعم في حالة وجود مشكلة.'
    },
    {
      question: 'ما هو الفرق بين المؤثر ومبدع المحتوى؟',
      answer: 'المؤثر هو شخص مشهور بمتابعين كثر، بينما مبدع المحتوى (UGC Creator) يركز على إنتاج محتوى إعلاني احترافي حتى لو لم يكن مشهوراً. كلاهما مهم للحملات الإعلانية.'
    },
    {
      question: 'كيف أضمن جودة الخدمة؟',
      answer: 'نوفر نظام تقييمات شامل، ومعاينة أعمال سابقة، وضمان استرداد الأموال في حالة عدم الرضا. كما يمكنك التواصل مع المؤثر قبل إتمام الطلب.'
    }
  ]

  const quickActions = [
    {
      icon: MessageCircle,
      title: 'الدردشة المباشرة',
      description: 'تحدث مع فريق الدعم',
      action: () => alert('ميزة الدردشة المباشرة قريباً!')
    },
    {
      icon: Phone,
      title: 'اتصل بنا',
      description: '+966 11 123 4567',
      action: () => window.open('tel:+966111234567')
    },
    {
      icon: Mail,
      title: 'البريد الإلكتروني',
      description: '<EMAIL>',
      action: () => router.push('/contact')
    }
  ]

  const resources = [
    {
      icon: Book,
      title: 'دليل المستخدم',
      description: 'دليل شامل لاستخدام المنصة'
    },
    {
      icon: Video,
      title: 'فيديوهات تعليمية',
      description: 'شروحات مرئية خطوة بخطوة'
    },
    {
      icon: FileText,
      title: 'المقالات',
      description: 'نصائح وإرشادات مفيدة'
    }
  ]

  const filteredFaqs = faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">المساعدة والدعم</h1>
            <p className="text-gray-600">نحن هنا لمساعدتك</p>
          </div>
        </div>

        {/* Search */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <div className="text-center mb-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <HelpCircle className="w-8 h-8 text-blue-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                كيف يمكننا مساعدتك؟
              </h2>
              <p className="text-gray-600 mb-4">
                ابحث في قاعدة المعرفة أو تواصل مع فريق الدعم
              </p>
            </div>
            
            <Input
              placeholder="ابحث عن إجابة..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="w-5 h-5" />}
            />
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="grid grid-cols-1 gap-3">
            {quickActions.map((action, index) => {
              const Icon = action.icon
              return (
                <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow" onClick={action.action}>
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <Icon className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{action.title}</h3>
                      <p className="text-sm text-gray-600">{action.description}</p>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>
        </motion.div>

        {/* Help Categories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              فئات المساعدة
            </h3>
            
            <div className="grid grid-cols-2 gap-3">
              {helpCategories.map((category, index) => {
                const Icon = category.icon
                return (
                  <div
                    key={index}
                    className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors cursor-pointer"
                  >
                    <div className={`w-10 h-10 ${category.color} rounded-lg flex items-center justify-center mb-3`}>
                      <Icon className="w-5 h-5" />
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1 text-sm">
                      {category.title}
                    </h4>
                    <p className="text-xs text-gray-600">
                      {category.description}
                    </p>
                  </div>
                )
              })}
            </div>
          </Card>
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              الأسئلة الشائعة
            </h3>
            
            <div className="space-y-3">
              {filteredFaqs.map((faq, index) => (
                <div
                  key={index}
                  className="border border-gray-200 rounded-lg overflow-hidden"
                >
                  <button
                    onClick={() => setExpandedFaq(expandedFaq === index ? null : index)}
                    className="w-full p-4 text-right hover:bg-gray-50 transition-colors flex items-center justify-between"
                  >
                    <span className="font-medium text-gray-900">
                      {faq.question}
                    </span>
                    {expandedFaq === index ? (
                      <ChevronUp className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  
                  {expandedFaq === index && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="px-4 pb-4"
                    >
                      <p className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </motion.div>
                  )}
                </div>
              ))}
            </div>
            
            {filteredFaqs.length === 0 && searchQuery && (
              <div className="text-center py-8">
                <p className="text-gray-500">لم نجد نتائج لبحثك</p>
                <Button
                  variant="ghost"
                  size="sm"
                  className="mt-2"
                  onClick={() => setSearchQuery('')}
                >
                  مسح البحث
                </Button>
              </div>
            )}
          </Card>
        </motion.div>

        {/* Resources */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              مصادر مفيدة
            </h3>
            
            <div className="space-y-3">
              {resources.map((resource, index) => {
                const Icon = resource.icon
                return (
                  <div
                    key={index}
                    className="flex items-center space-x-3 rtl:space-x-reverse p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer"
                  >
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Icon className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {resource.title}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {resource.description}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          </Card>
        </motion.div>

        {/* Emergency Contact */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card className="bg-red-50 border-red-200">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <Zap className="w-6 h-6 text-red-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-red-900 mb-1">
                  حالة طوارئ؟
                </h3>
                <p className="text-red-700 text-sm mb-3">
                  للمشاكل العاجلة أو الطوارئ، تواصل معنا فوراً
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-red-300 text-red-700 hover:bg-red-100"
                  onClick={() => window.open('tel:+966111234567')}
                >
                  اتصال طوارئ
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Feedback */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card className="bg-blue-50 border-blue-200">
            <div className="text-center">
              <h3 className="font-semibold text-blue-900 mb-2">
                لم تجد ما تبحث عنه؟
              </h3>
              <p className="text-blue-700 text-sm mb-4">
                ساعدنا في تحسين قسم المساعدة بإرسال اقتراحاتك
              </p>
              <Button
                variant="primary"
                size="sm"
                onClick={() => router.push('/contact')}
              >
                إرسال ملاحظات
              </Button>
            </div>
          </Card>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
