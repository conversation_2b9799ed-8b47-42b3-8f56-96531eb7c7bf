'use client'

import React, { useEffect, useState } from 'react'

interface ClientProviderProps {
  children: React.ReactNode
}

export default function ClientProvider({ children }: ClientProviderProps) {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  if (!hasMounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-2xl">🇸🇦</span>
          </div>
          <div className="loading-spinner mx-auto mb-4" />
          <p className="text-gray-600">جاري تحميل المنصة...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
