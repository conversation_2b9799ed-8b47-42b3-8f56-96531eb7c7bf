'use client'

import React, { useEffect, useState } from 'react'
import { useTheme } from '@/lib/theme'

interface ClientProviderProps {
  children: React.ReactNode
}

export default function ClientProvider({ children }: ClientProviderProps) {
  const [hasMounted, setHasMounted] = useState(false)
  const { isDarkMode } = useTheme()

  useEffect(() => {
    setHasMounted(true)
    // Apply dark mode class to document
    if (typeof window !== 'undefined') {
      document.documentElement.classList.add('dark')
    }
  }, [])

  useEffect(() => {
    if (hasMounted && typeof window !== 'undefined') {
      if (isDarkMode) {
        document.documentElement.classList.add('dark')
        document.documentElement.classList.remove('light')
      } else {
        document.documentElement.classList.add('light')
        document.documentElement.classList.remove('dark')
      }
    }
  }, [isDarkMode, hasMounted])

  if (!hasMounted) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-2xl">🇸🇦</span>
          </div>
          <div className="loading-spinner mx-auto mb-4" />
          <p className="text-slate-300">جاري تحميل المنصة...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
