'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useAuth, useUI, useNotifications } from '@/lib/hooks'
import { Bell, Menu, Search, User } from 'lucide-react'
import Button from '../ui/Button'
import { useRouter } from 'next/navigation'

const Header: React.FC = () => {
  const { user, isAuthenticated, logout } = useAuth()
  const { isMobileMenuOpen, setMobileMenuOpen } = useUI()
  const { notifications } = useNotifications()
  const router = useRouter()

  const unreadCount = notifications.filter(n => !n.isRead).length

  const handleProfileClick = () => {
    if (isAuthenticated && user) {
      switch (user.type) {
        case 'merchant':
          router.push('/merchant/dashboard')
          break
        case 'influencer':
        case 'ugc_creator':
          router.push('/creator/dashboard')
          break
        case 'admin':
          router.push('/admin/dashboard')
          break
        default:
          router.push('/profile')
      }
    } else {
      router.push('/auth/login')
    }
  }

  return (
    <header className="fixed top-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white/95 backdrop-blur-md border-b border-gray-100 z-50">
      <div className="flex items-center justify-between px-4 py-3">
        {/* Logo */}
        <motion.div
          className="flex items-center space-x-2 rtl:space-x-reverse"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">🇸🇦</span>
          </div>
          <div className="text-right rtl:text-left">
            <h1 className="text-lg font-bold text-gray-900">منصة المؤثرين</h1>
            <p className="text-xs text-gray-500">السعودية</p>
          </div>
        </motion.div>

        {/* Actions */}
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          {isAuthenticated ? (
            <>
              {/* Search */}
              <motion.button
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => router.push('/search')}
              >
                <Search className="w-5 h-5 text-gray-600" />
              </motion.button>

              {/* Notifications */}
              <motion.button
                className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => router.push('/notifications')}
              >
                <Bell className="w-5 h-5 text-gray-600" />
                {unreadCount > 0 && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
                  >
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </motion.span>
                )}
              </motion.button>

              {/* Profile */}
              <motion.button
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={handleProfileClick}
              >
                {user?.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-6 h-6 rounded-full object-cover"
                  />
                ) : (
                  <User className="w-5 h-5 text-gray-600" />
                )}
              </motion.button>

              {/* Menu */}
              <motion.button
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setMobileMenuOpen(!isMobileMenuOpen)}
              >
                <Menu className="w-5 h-5 text-gray-600" />
              </motion.button>
            </>
          ) : (
            <div className="flex space-x-2 rtl:space-x-reverse">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/auth/login')}
              >
                تسجيل الدخول
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={() => router.push('/auth/register')}
              >
                إنشاء حساب
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && isAuthenticated && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute top-full left-0 right-0 bg-white border-b border-gray-100 shadow-lg"
        >
          <div className="p-4 space-y-2">
            <button
              className="w-full text-right p-3 rounded-lg hover:bg-gray-50 transition-colors"
              onClick={() => {
                router.push('/profile')
                setMobileMenuOpen(false)
              }}
            >
              الملف الشخصي
            </button>
            <button
              className="w-full text-right p-3 rounded-lg hover:bg-gray-50 transition-colors"
              onClick={() => {
                router.push('/settings')
                setMobileMenuOpen(false)
              }}
            >
              الإعدادات
            </button>
            <button
              className="w-full text-right p-3 rounded-lg hover:bg-gray-50 transition-colors"
              onClick={() => {
                router.push('/help')
                setMobileMenuOpen(false)
              }}
            >
              المساعدة
            </button>
            <hr className="my-2" />
            <button
              className="w-full text-right p-3 rounded-lg hover:bg-red-50 text-red-600 transition-colors"
              onClick={() => {
                logout()
                setMobileMenuOpen(false)
                router.push('/')
              }}
            >
              تسجيل الخروج
            </button>
          </div>
        </motion.div>
      )}
    </header>
  )
}

export default Header
