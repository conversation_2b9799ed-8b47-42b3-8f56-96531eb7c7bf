'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useAuth, useUI } from '@/lib/store'
import { useRouter, usePathname } from 'next/navigation'
import { 
  Home, 
  Search, 
  PlusCircle, 
  MessageCircle, 
  User,
  BarChart3,
  Users,
  Settings
} from 'lucide-react'

const BottomNavigation: React.FC = () => {
  const { user } = useAuth()
  const { setCurrentPage } = useUI()
  const router = useRouter()
  const pathname = usePathname()
  
  const getNavigationItems = () => {
    if (!user) return []
    
    switch (user.type) {
      case 'merchant':
        return [
          { id: 'home', label: 'الرئيسية', icon: Home, path: '/' },
          { id: 'search', label: 'البحث', icon: Search, path: '/search' },
          { id: 'create', label: 'إنشاء حملة', icon: PlusCircle, path: '/merchant/create-campaign' },
          { id: 'dashboard', label: 'لوحة التحكم', icon: BarChart3, path: '/merchant/dashboard' },
          { id: 'profile', label: 'الملف الشخصي', icon: User, path: '/merchant/profile' },
        ]
      
      case 'influencer':
      case 'ugc_creator':
        return [
          { id: 'home', label: 'الرئيسية', icon: Home, path: '/' },
          { id: 'search', label: 'الفرص', icon: Search, path: '/creator/opportunities' },
          { id: 'messages', label: 'الرسائل', icon: MessageCircle, path: '/creator/messages' },
          { id: 'dashboard', label: 'لوحة التحكم', icon: BarChart3, path: '/creator/dashboard' },
          { id: 'profile', label: 'الملف الشخصي', icon: User, path: '/creator/profile' },
        ]
      
      case 'admin':
        return [
          { id: 'dashboard', label: 'لوحة التحكم', icon: BarChart3, path: '/admin/dashboard' },
          { id: 'users', label: 'المستخدمين', icon: Users, path: '/admin/users' },
          { id: 'campaigns', label: 'الحملات', icon: Search, path: '/admin/campaigns' },
          { id: 'payments', label: 'المدفوعات', icon: PlusCircle, path: '/admin/payments' },
          { id: 'settings', label: 'الإعدادات', icon: Settings, path: '/admin/settings' },
        ]
      
      default:
        return []
    }
  }
  
  const navigationItems = getNavigationItems()
  
  const handleNavigation = (item: any) => {
    setCurrentPage(item.id)
    router.push(item.path)
  }
  
  const isActive = (path: string) => {
    if (path === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(path)
  }
  
  return (
    <div className="bg-white border-t border-gray-100 px-2 py-1">
      <div className="flex items-center justify-around">
        {navigationItems.map((item) => {
          const Icon = item.icon
          const active = isActive(item.path)
          
          return (
            <motion.button
              key={item.id}
              className={`flex flex-col items-center justify-center p-2 rounded-lg transition-all duration-200 ${
                active 
                  ? 'text-green-600 bg-green-50' 
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleNavigation(item)}
            >
              <motion.div
                animate={{
                  scale: active ? 1.1 : 1,
                  color: active ? '#059669' : '#6B7280'
                }}
                transition={{ duration: 0.2 }}
              >
                <Icon className="w-5 h-5 mb-1" />
              </motion.div>
              <span className={`text-xs font-medium ${
                active ? 'text-green-600' : 'text-gray-500'
              }`}>
                {item.label}
              </span>
              
              {active && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-green-600 rounded-full"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}
            </motion.button>
          )
        })}
      </div>
    </div>
  )
}

export default BottomNavigation
