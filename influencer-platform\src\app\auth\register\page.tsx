'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/lib/store'
import MobileLayout from '@/components/layout/MobileLayout'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Card from '@/components/ui/Card'
import {
  Mail,
  Lock,
  Eye,
  EyeOff,
  ArrowRight,
  User,
  Phone,
  Store,
  Star
} from 'lucide-react'
import { validateEmail, validatePhone, getSaudiCities } from '@/lib/utils'

export default function RegisterPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login, setLoading } = useAuth()

  const [userType, setUserType] = useState<'merchant' | 'creator'>('merchant')
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState({
    // Basic info
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',

    // Merchant specific
    businessName: '',
    businessType: '',
    city: '',

    // Creator specific
    displayName: '',
    category: 'influencer' as 'influencer' | 'ugc_creator',
    bio: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const type = searchParams.get('type')
    if (type === 'merchant' || type === 'creator') {
      setUserType(type)
    }
  }, [searchParams])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateStep1 = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'الاسم مطلوب'
    }

    if (!formData.email) {
      newErrors.email = 'البريد الإلكتروني مطلوب'
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }

    if (!formData.phone) {
      newErrors.phone = 'رقم الجوال مطلوب'
    } else if (!validatePhone(formData.phone)) {
      newErrors.phone = 'رقم الجوال غير صحيح'
    }

    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة'
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'تأكيد كلمة المرور مطلوب'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمة المرور غير متطابقة'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validateStep2 = () => {
    const newErrors: Record<string, string> = {}

    if (userType === 'merchant') {
      if (!formData.businessName.trim()) {
        newErrors.businessName = 'اسم النشاط التجاري مطلوب'
      }
      if (!formData.businessType.trim()) {
        newErrors.businessType = 'نوع النشاط التجاري مطلوب'
      }
      if (!formData.city) {
        newErrors.city = 'المدينة مطلوبة'
      }
    } else {
      if (!formData.displayName.trim()) {
        newErrors.displayName = 'الاسم المعروض مطلوب'
      }
      if (!formData.bio.trim()) {
        newErrors.bio = 'نبذة تعريفية مطلوبة'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (step === 1 && validateStep1()) {
      setStep(2)
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateStep2()) return

    setIsLoading(true)
    setLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mock user data - in real app, this would come from API
      const mockUser = {
        id: '1',
        email: formData.email,
        phone: formData.phone,
        name: formData.name,
        type: userType === 'merchant' ? 'merchant' as const : 'influencer' as const,
        isVerified: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      login(mockUser)

      // Redirect based on user type
      if (userType === 'merchant') {
        router.push('/merchant/dashboard')
      } else {
        router.push('/creator/dashboard')
      }
    } catch (error) {
      setErrors({ general: 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.' })
    } finally {
      setIsLoading(false)
      setLoading(false)
    }
  }

  const cities = getSaudiCities()

  return (
    <MobileLayout showHeader={false} showBottomNav={false}>
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-md"
        >
          {/* Logo */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl">🇸🇦</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              إنشاء حساب جديد
            </h1>
            <p className="text-gray-600">
              {userType === 'merchant' ? 'انضم كتاجر' : 'انضم كمؤثر أو مبدع محتوى'}
            </p>
          </div>

          {/* User Type Selection */}
          {step === 1 && (
            <Card className="mb-6">
              <div className="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  onClick={() => setUserType('merchant')}
                  className={`p-4 rounded-xl border-2 transition-all ${
                    userType === 'merchant'
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <Store className={`w-6 h-6 mx-auto mb-2 ${
                    userType === 'merchant' ? 'text-green-600' : 'text-gray-400'
                  }`} />
                  <div className={`text-sm font-medium ${
                    userType === 'merchant' ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    تاجر
                  </div>
                </button>

                <button
                  type="button"
                  onClick={() => setUserType('creator')}
                  className={`p-4 rounded-xl border-2 transition-all ${
                    userType === 'creator'
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <Star className={`w-6 h-6 mx-auto mb-2 ${
                    userType === 'creator' ? 'text-green-600' : 'text-gray-400'
                  }`} />
                  <div className={`text-sm font-medium ${
                    userType === 'creator' ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    مؤثر / مبدع
                  </div>
                </button>
              </div>
            </Card>
          )}

          <Card>
            {/* Progress Indicator */}
            <div className="flex items-center justify-center mb-6">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= 1 ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'
                }`}>
                  1
                </div>
                <div className={`w-8 h-1 ${step >= 2 ? 'bg-green-500' : 'bg-gray-200'}`} />
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= 2 ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'
                }`}>
                  2
                </div>
              </div>
            </div>

            <form onSubmit={step === 2 ? handleSubmit : (e) => { e.preventDefault(); handleNext() }}>
              {errors.general && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg mb-6">
                  <p className="text-red-600 text-sm">{errors.general}</p>
                </div>
              )}

              {step === 1 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="space-y-4"
                >
                  <Input
                    label="الاسم الكامل"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    error={errors.name}
                    leftIcon={<User className="w-5 h-5" />}
                    placeholder="أدخل اسمك الكامل"
                    disabled={isLoading}
                  />

                  <Input
                    label="البريد الإلكتروني"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    error={errors.email}
                    leftIcon={<Mail className="w-5 h-5" />}
                    placeholder="<EMAIL>"
                    disabled={isLoading}
                  />

                  <Input
                    label="رقم الجوال"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    error={errors.phone}
                    leftIcon={<Phone className="w-5 h-5" />}
                    placeholder="05xxxxxxxx"
                    disabled={isLoading}
                  />

                  <Input
                    label="كلمة المرور"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    error={errors.password}
                    leftIcon={<Lock className="w-5 h-5" />}
                    rightIcon={
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    }
                    placeholder="كلمة المرور"
                    disabled={isLoading}
                  />

                  <Input
                    label="تأكيد كلمة المرور"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    error={errors.confirmPassword}
                    leftIcon={<Lock className="w-5 h-5" />}
                    rightIcon={
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    }
                    placeholder="تأكيد كلمة المرور"
                    disabled={isLoading}
                  />
                </motion.div>
              )}

              {step === 2 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="space-y-4"
                >
                  {userType === 'merchant' ? (
                    <>
                      <Input
                        label="اسم النشاط التجاري"
                        value={formData.businessName}
                        onChange={(e) => handleInputChange('businessName', e.target.value)}
                        error={errors.businessName}
                        leftIcon={<Store className="w-5 h-5" />}
                        placeholder="اسم متجرك أو شركتك"
                        disabled={isLoading}
                      />

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          نوع النشاط التجاري
                        </label>
                        <select
                          value={formData.businessType}
                          onChange={(e) => handleInputChange('businessType', e.target.value)}
                          className={`input-field ${errors.businessType ? 'border-red-500' : ''}`}
                          disabled={isLoading}
                        >
                          <option value="">اختر نوع النشاط</option>
                          <option value="fashion">الموضة والأزياء</option>
                          <option value="beauty">الجمال والعناية</option>
                          <option value="food">الطعام والمطاعم</option>
                          <option value="tech">التقنية والإلكترونيات</option>
                          <option value="health">الصحة والعافية</option>
                          <option value="sports">الرياضة واللياقة</option>
                          <option value="travel">السفر والسياحة</option>
                          <option value="education">التعليم والثقافة</option>
                          <option value="real_estate">العقارات</option>
                          <option value="automotive">السيارات</option>
                          <option value="other">أخرى</option>
                        </select>
                        {errors.businessType && (
                          <p className="mt-1 text-sm text-red-600">{errors.businessType}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          المدينة
                        </label>
                        <select
                          value={formData.city}
                          onChange={(e) => handleInputChange('city', e.target.value)}
                          className={`input-field ${errors.city ? 'border-red-500' : ''}`}
                          disabled={isLoading}
                        >
                          <option value="">اختر المدينة</option>
                          {cities.map(city => (
                            <option key={city} value={city}>{city}</option>
                          ))}
                        </select>
                        {errors.city && (
                          <p className="mt-1 text-sm text-red-600">{errors.city}</p>
                        )}
                      </div>
                    </>
                  ) : (
                    <>
                      <Input
                        label="الاسم المعروض"
                        value={formData.displayName}
                        onChange={(e) => handleInputChange('displayName', e.target.value)}
                        error={errors.displayName}
                        leftIcon={<Star className="w-5 h-5" />}
                        placeholder="الاسم الذي سيظهر للتجار"
                        disabled={isLoading}
                      />

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          نوع المحتوى
                        </label>
                        <div className="grid grid-cols-2 gap-3">
                          <button
                            type="button"
                            onClick={() => handleInputChange('category', 'influencer')}
                            className={`p-3 rounded-lg border-2 text-sm transition-all ${
                              formData.category === 'influencer'
                                ? 'border-green-500 bg-green-50 text-green-600'
                                : 'border-gray-200 hover:border-gray-300 text-gray-600'
                            }`}
                          >
                            مؤثر مشهور
                          </button>
                          <button
                            type="button"
                            onClick={() => handleInputChange('category', 'ugc_creator')}
                            className={`p-3 rounded-lg border-2 text-sm transition-all ${
                              formData.category === 'ugc_creator'
                                ? 'border-green-500 bg-green-50 text-green-600'
                                : 'border-gray-200 hover:border-gray-300 text-gray-600'
                            }`}
                          >
                            مبدع محتوى UGC
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          نبذة تعريفية
                        </label>
                        <textarea
                          value={formData.bio}
                          onChange={(e) => handleInputChange('bio', e.target.value)}
                          className={`input-field min-h-[100px] resize-none ${errors.bio ? 'border-red-500' : ''}`}
                          placeholder="اكتب نبذة مختصرة عن نفسك ونوع المحتوى الذي تقدمه"
                          disabled={isLoading}
                        />
                        {errors.bio && (
                          <p className="mt-1 text-sm text-red-600">{errors.bio}</p>
                        )}
                      </div>
                    </>
                  )}
                </motion.div>
              )}

              <div className="flex space-x-3 rtl:space-x-reverse pt-6">
                {step > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleBack}
                    disabled={isLoading}
                    className="flex-1"
                  >
                    السابق
                  </Button>
                )}

                <Button
                  type="submit"
                  variant="primary"
                  isLoading={isLoading}
                  rightIcon={<ArrowRight className="w-5 h-5" />}
                  className="flex-1"
                >
                  {step === 1 ? 'التالي' : 'إنشاء الحساب'}
                </Button>
              </div>
            </form>

            <div className="mt-6 text-center">
              <p className="text-gray-600">
                لديك حساب بالفعل؟{' '}
                <button
                  onClick={() => router.push('/auth/login')}
                  className="text-green-600 hover:text-green-700 font-medium"
                >
                  تسجيل الدخول
                </button>
              </p>
            </div>
          </Card>

          <div className="mt-6 text-center">
            <button
              onClick={() => router.push('/')}
              className="text-gray-500 hover:text-gray-700 text-sm"
            >
              العودة إلى الصفحة الرئيسية
            </button>
          </div>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
