'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '@/lib/store'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { 
  Users, 
  TrendingUp, 
  DollarSign, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Eye,
  Settings,
  BarChart3,
  UserCheck,
  CreditCard,
  MessageSquare
} from 'lucide-react'
import { formatPrice, formatDate, getStatusColor, getStatusText } from '@/lib/utils'

export default function AdminDashboard() {
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  
  // Mock admin stats
  const [stats, setStats] = useState({
    totalUsers: 1250,
    totalMerchants: 450,
    totalInfluencers: 800,
    totalCampaigns: 2340,
    activeCampaigns: 156,
    completedCampaigns: 2050,
    totalRevenue: 850000,
    monthlyRevenue: 125000,
    pendingPayments: 45000,
    platformFees: 85000
  })
  
  // Mock recent activities
  const [recentActivities, setRecentActivities] = useState([
    {
      id: '1',
      type: 'campaign_created',
      user: 'أحمد محمد',
      action: 'أنشأ حملة جديدة',
      details: 'إعلان منتج العناية بالبشرة',
      amount: 2500,
      timestamp: new Date('2024-01-15T10:30:00'),
      status: 'pending'
    },
    {
      id: '2',
      type: 'payment_completed',
      user: 'سارة أحمد',
      action: 'تم إكمال الدفع',
      details: 'حملة إطلاق المجموعة الجديدة',
      amount: 5000,
      timestamp: new Date('2024-01-15T09:15:00'),
      status: 'completed'
    },
    {
      id: '3',
      type: 'user_registered',
      user: 'محمد علي',
      action: 'انضم كمؤثر جديد',
      details: 'مؤثر في مجال التقنية',
      timestamp: new Date('2024-01-15T08:45:00'),
      status: 'approved'
    },
    {
      id: '4',
      type: 'campaign_completed',
      user: 'نورا خالد',
      action: 'أكملت حملة إعلانية',
      details: 'تجربة مطعم جديد',
      amount: 1800,
      timestamp: new Date('2024-01-14T16:20:00'),
      status: 'completed'
    }
  ])
  
  // Mock pending approvals
  const [pendingApprovals, setPendingApprovals] = useState([
    {
      id: '1',
      type: 'influencer_verification',
      name: 'ليلى أحمد',
      details: 'طلب توثيق حساب مؤثر',
      timestamp: new Date('2024-01-15T11:00:00')
    },
    {
      id: '2',
      type: 'campaign_review',
      name: 'متجر الأناقة',
      details: 'مراجعة حملة إعلانية جديدة',
      timestamp: new Date('2024-01-15T10:45:00')
    },
    {
      id: '3',
      type: 'payment_dispute',
      name: 'خالد محمد',
      details: 'نزاع حول دفعة مالية',
      timestamp: new Date('2024-01-15T09:30:00')
    }
  ])
  
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }
    
    if (user?.type !== 'admin') {
      router.push('/')
      return
    }
    
    // Simulate loading
    setTimeout(() => setIsLoading(false), 1000)
  }, [isAuthenticated, user, router])
  
  if (isLoading) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }
  
  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            لوحة تحكم المشرف
          </h1>
          <p className="text-gray-600">
            إدارة شاملة للمنصة والمستخدمين
          </p>
        </div>
        
        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stats.totalUsers.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">
                إجمالي المستخدمين
              </div>
            </Card>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {formatPrice(stats.totalRevenue)}
              </div>
              <div className="text-sm text-gray-600">
                إجمالي الإيرادات
              </div>
            </Card>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stats.totalCampaigns.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">
                إجمالي الحملات
              </div>
            </Card>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stats.activeCampaigns}
              </div>
              <div className="text-sm text-gray-600">
                حملات نشطة
              </div>
            </Card>
          </motion.div>
        </div>
        
        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              إجراءات سريعة
            </h2>
            
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="primary"
                size="sm"
                leftIcon={<Users className="w-4 h-4" />}
                onClick={() => router.push('/admin/users')}
                className="text-sm"
              >
                إدارة المستخدمين
              </Button>
              
              <Button
                variant="secondary"
                size="sm"
                leftIcon={<BarChart3 className="w-4 h-4" />}
                onClick={() => router.push('/admin/campaigns')}
                className="text-sm"
              >
                إدارة الحملات
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                leftIcon={<CreditCard className="w-4 h-4" />}
                onClick={() => router.push('/admin/payments')}
                className="text-sm"
              >
                إدارة المدفوعات
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                leftIcon={<Settings className="w-4 h-4" />}
                onClick={() => router.push('/admin/settings')}
                className="text-sm"
              >
                إعدادات المنصة
              </Button>
            </div>
          </Card>
        </motion.div>
        
        {/* Pending Approvals */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                طلبات تحتاج موافقة
              </h2>
              <Badge variant="warning" size="sm">
                {pendingApprovals.length}
              </Badge>
            </div>
            
            <div className="space-y-3">
              {pendingApprovals.map((approval, index) => (
                <motion.div
                  key={approval.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 + index * 0.1 }}
                  className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200"
                >
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                      <AlertCircle className="w-5 h-5 text-yellow-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {approval.name}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {approval.details}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatDate(approval.timestamp)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <Button variant="ghost" size="sm">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="primary" size="sm">
                      موافقة
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          </Card>
        </motion.div>
        
        {/* Recent Activities */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                النشاطات الأخيرة
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/admin/activities')}
              >
                عرض الكل
              </Button>
            </div>
            
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.9 + index * 0.1 }}
                  className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 rounded-lg"
                >
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                    {activity.user.charAt(0)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900">
                      {activity.user}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {activity.action} - {activity.details}
                    </p>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                      {activity.amount && (
                        <span className="text-sm font-medium text-green-600">
                          {formatPrice(activity.amount)}
                        </span>
                      )}
                      <Badge
                        variant={
                          activity.status === 'completed' ? 'success' :
                          activity.status === 'approved' ? 'info' :
                          activity.status === 'pending' ? 'warning' : 'default'
                        }
                        size="sm"
                      >
                        {getStatusText(activity.status)}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {formatDate(activity.timestamp)}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </Card>
        </motion.div>
        
        {/* Financial Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
        >
          <Card>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              نظرة مالية
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">الإيرادات الشهرية</span>
                <span className="text-lg font-semibold text-green-600">
                  {formatPrice(stats.monthlyRevenue)}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">المدفوعات المعلقة</span>
                <span className="text-lg font-semibold text-yellow-600">
                  {formatPrice(stats.pendingPayments)}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">رسوم المنصة</span>
                <span className="text-lg font-semibold text-blue-600">
                  {formatPrice(stats.platformFees)}
                </span>
              </div>
              
              <div className="pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-900">صافي الربح</span>
                  <span className="text-xl font-bold text-green-600">
                    {formatPrice(stats.platformFees - stats.pendingPayments)}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
