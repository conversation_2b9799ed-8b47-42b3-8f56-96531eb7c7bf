'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import { 
  ArrowLeft,
  MessageCircle,
  Send,
  Calendar,
  DollarSign,
  Package,
  CheckCircle,
  Clock,
  Star
} from 'lucide-react'
import { formatPrice } from '@/lib/utils'

export default function ContactInfluencerPage() {
  const router = useRouter()
  const params = useParams()
  const { user, isAuthenticated } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [step, setStep] = useState(1) // 1: Service Selection, 2: Details, 3: Confirmation
  
  const [formData, setFormData] = useState({
    serviceType: '',
    message: '',
    budget: '',
    deadline: '',
    requirements: ''
  })

  // Mock influencer data
  const influencer = {
    id: params.id,
    name: 'سارة أحمد',
    avatar: '',
    rating: 4.9,
    completedCampaigns: 45,
    responseTime: '2 ساعة',
    isVerified: true,
    services: [
      { id: '1', type: 'instagram_reel', name: 'ريل إنستغرام', price: 2500, duration: '30-60 ثانية' },
      { id: '2', type: 'full_story', name: 'ستوري كامل', price: 1800, duration: '24 ساعة' },
      { id: '3', type: 'single_snap', name: 'سناب واحد', price: 800, duration: '24 ساعة' },
      { id: '4', type: 'tiktok_video', name: 'فيديو تيك توك', price: 2200, duration: '15-30 ثانية' }
    ]
  }

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, router])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleServiceSelect = (serviceId: string) => {
    const service = influencer.services.find(s => s.id === serviceId)
    if (service) {
      setFormData(prev => ({ 
        ...prev, 
        serviceType: serviceId,
        budget: service.price.toString()
      }))
      setStep(2)
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Show success and redirect
      alert('تم إرسال طلبك بنجاح! سيتم التواصل معك قريباً.')
      router.push('/merchant/dashboard')
    } catch (error) {
      alert('حدث خطأ أثناء إرسال الطلب')
    } finally {
      setIsLoading(false)
    }
  }

  const selectedService = influencer.services.find(s => s.id === formData.serviceType)

  if (!user) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">تواصل مع المؤثر</h1>
            <p className="text-gray-600">{influencer.name}</p>
          </div>
        </div>

        {/* Influencer Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {influencer.name.charAt(0)}
                </div>
                {influencer.isVerified && (
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">✓</span>
                  </div>
                )}
              </div>
              
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-1">
                  {influencer.name}
                </h3>
                
                <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-600">
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span>{influencer.rating}</span>
                  </div>
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>{influencer.completedCampaigns} حملة</span>
                  </div>
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <Clock className="w-4 h-4 text-blue-500" />
                    <span>يرد خلال {influencer.responseTime}</span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {[1, 2, 3].map((stepNumber) => (
              <React.Fragment key={stepNumber}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= stepNumber ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'
                }`}>
                  {stepNumber}
                </div>
                {stepNumber < 3 && (
                  <div className={`w-8 h-1 ${step > stepNumber ? 'bg-green-500' : 'bg-gray-200'}`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Step 1: Service Selection */}
        {step === 1 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-4"
          >
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                اختر نوع الخدمة
              </h3>
              
              <div className="space-y-3">
                {influencer.services.map((service) => (
                  <button
                    key={service.id}
                    onClick={() => handleServiceSelect(service.id)}
                    className="w-full p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-all text-right"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-1">
                          {service.name}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {service.duration}
                        </p>
                      </div>
                      <div className="text-left">
                        <div className="text-lg font-bold text-green-600">
                          {formatPrice(service.price)}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </Card>
          </motion.div>
        )}

        {/* Step 2: Details */}
        {step === 2 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-4"
          >
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                تفاصيل الطلب
              </h3>
              
              {selectedService && (
                <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-green-900">
                      {selectedService.name}
                    </span>
                    <span className="font-bold text-green-600">
                      {formatPrice(selectedService.price)}
                    </span>
                  </div>
                </div>
              )}
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رسالة للمؤثر
                  </label>
                  <textarea
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    className="input-field min-h-[100px] resize-none"
                    placeholder="اكتب رسالة تعريفية عن مشروعك والهدف من الحملة..."
                  />
                </div>
                
                <Input
                  label="الموعد المطلوب"
                  type="date"
                  value={formData.deadline}
                  onChange={(e) => handleInputChange('deadline', e.target.value)}
                  leftIcon={<Calendar className="w-5 h-5" />}
                />
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    متطلبات إضافية
                  </label>
                  <textarea
                    value={formData.requirements}
                    onChange={(e) => handleInputChange('requirements', e.target.value)}
                    className="input-field min-h-[80px] resize-none"
                    placeholder="أي متطلبات خاصة أو تفاصيل إضافية..."
                  />
                </div>
              </div>
            </Card>
            
            <div className="flex space-x-3 rtl:space-x-reverse">
              <Button
                variant="outline"
                onClick={() => setStep(1)}
                className="flex-1"
              >
                السابق
              </Button>
              <Button
                variant="primary"
                onClick={() => setStep(3)}
                disabled={!formData.message.trim()}
                className="flex-1"
              >
                التالي
              </Button>
            </div>
          </motion.div>
        )}

        {/* Step 3: Confirmation */}
        {step === 3 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-4"
          >
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                تأكيد الطلب
              </h3>
              
              <div className="space-y-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">ملخص الطلب</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>الخدمة:</span>
                      <span>{selectedService?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>السعر:</span>
                      <span className="font-medium">{formatPrice(selectedService?.price || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>الموعد:</span>
                      <span>{formData.deadline || 'غير محدد'}</span>
                    </div>
                  </div>
                </div>
                
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">ملاحظة مهمة</h4>
                  <p className="text-sm text-blue-800">
                    سيتم حجز المبلغ من حسابك وتحريره للمؤثر بعد إتمام الخدمة بنجاح.
                    يمكنك إلغاء الطلب قبل موافقة المؤثر.
                  </p>
                </div>
              </div>
            </Card>
            
            <div className="flex space-x-3 rtl:space-x-reverse">
              <Button
                variant="outline"
                onClick={() => setStep(2)}
                className="flex-1"
              >
                السابق
              </Button>
              <Button
                variant="primary"
                onClick={handleSubmit}
                isLoading={isLoading}
                leftIcon={<Send className="w-5 h-5" />}
                className="flex-1"
              >
                إرسال الطلب
              </Button>
            </div>
          </motion.div>
        )}
      </div>
    </MobileLayout>
  )
}
