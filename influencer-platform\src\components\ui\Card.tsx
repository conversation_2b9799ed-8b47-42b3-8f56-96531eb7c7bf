'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  hover?: boolean
  padding?: 'none' | 'sm' | 'md' | 'lg'
  shadow?: 'none' | 'sm' | 'md' | 'lg'
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({
    className,
    hover = true,
    padding = 'md',
    shadow = 'md',
    children,
    ...props
  }, ref) => {
    const paddingClasses = {
      none: '',
      sm: 'p-4',
      md: 'p-6',
      lg: 'p-8'
    }
    
    const shadowClasses = {
      none: '',
      sm: 'shadow-sm',
      md: 'shadow-md',
      lg: 'shadow-lg'
    }
    
    const baseClasses = 'bg-white rounded-2xl border border-gray-100 transition-all duration-200'
    
    const CardComponent = hover ? motion.div : 'div'
    const motionProps = hover ? {
      whileHover: { y: -2, scale: 1.01 },
      transition: { duration: 0.2 }
    } : {}
    
    return (
      <CardComponent
        ref={ref}
        className={cn(
          baseClasses,
          paddingClasses[padding],
          shadowClasses[shadow],
          hover && 'hover:shadow-xl cursor-pointer',
          className
        )}
        {...motionProps}
        {...props}
      >
        {children}
      </CardComponent>
    )
  }
)

Card.displayName = 'Card'

export default Card
