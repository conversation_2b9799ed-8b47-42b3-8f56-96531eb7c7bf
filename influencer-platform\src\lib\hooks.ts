import { useEffect, useState } from 'react'
import { useAuthStore, useAppStore } from './store'

// Custom hook to handle hydration issues with Zustand persist
export const useHydratedAuth = () => {
  const [hydrated, setHydrated] = useState(false)
  const auth = useAuthStore()

  useEffect(() => {
    setHydrated(true)
  }, [])

  if (!hydrated) {
    return {
      user: null,
      isAuthenticated: false,
      isLoading: true,
      login: auth.login,
      logout: auth.logout,
      updateUser: auth.updateUser,
      setLoading: auth.setLoading,
    }
  }

  return auth
}

// Custom hook for app state
export const useHydratedApp = () => {
  const [hydrated, setHydrated] = useState(false)
  const app = useAppStore()

  useEffect(() => {
    setHydrated(true)
  }, [])

  if (!hydrated) {
    return {
      ...app,
      isLoading: true,
    }
  }

  return app
}

// Individual hooks for better performance
export const useAuth = () => {
  const [hydrated, setHydrated] = useState(false)
  const user = useAuthStore((state) => state.user)
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  const isLoading = useAuthStore((state) => state.isLoading)
  const login = useAuthStore((state) => state.login)
  const logout = useAuthStore((state) => state.logout)
  const updateUser = useAuthStore((state) => state.updateUser)
  const setLoading = useAuthStore((state) => state.setLoading)

  useEffect(() => {
    setHydrated(true)
  }, [])

  if (!hydrated) {
    return {
      user: null,
      isAuthenticated: false,
      isLoading: true,
      login,
      logout,
      updateUser,
      setLoading,
    }
  }

  return {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    updateUser,
    setLoading,
  }
}

export const useInfluencers = () => {
  const influencers = useAppStore((state) => state.influencers)
  const setInfluencers = useAppStore((state) => state.setInfluencers)
  const addInfluencer = useAppStore((state) => state.addInfluencer)
  const updateInfluencer = useAppStore((state) => state.updateInfluencer)

  return {
    influencers,
    setInfluencers,
    addInfluencer,
    updateInfluencer,
  }
}

export const useCampaigns = () => {
  const campaigns = useAppStore((state) => state.campaigns)
  const setCampaigns = useAppStore((state) => state.setCampaigns)
  const addCampaign = useAppStore((state) => state.addCampaign)
  const updateCampaign = useAppStore((state) => state.updateCampaign)
  const deleteCampaign = useAppStore((state) => state.deleteCampaign)

  return {
    campaigns,
    setCampaigns,
    addCampaign,
    updateCampaign,
    deleteCampaign,
  }
}

export const useNotifications = () => {
  const notifications = useAppStore((state) => state.notifications)
  const setNotifications = useAppStore((state) => state.setNotifications)
  const addNotification = useAppStore((state) => state.addNotification)
  const markNotificationAsRead = useAppStore((state) => state.markNotificationAsRead)
  const clearNotifications = useAppStore((state) => state.clearNotifications)

  return {
    notifications,
    setNotifications,
    addNotification,
    markNotificationAsRead,
    clearNotifications,
  }
}

export const useUI = () => {
  const currentPage = useAppStore((state) => state.currentPage)
  const setCurrentPage = useAppStore((state) => state.setCurrentPage)
  const isMobileMenuOpen = useAppStore((state) => state.isMobileMenuOpen)
  const setMobileMenuOpen = useAppStore((state) => state.setMobileMenuOpen)
  const isLoading = useAppStore((state) => state.isLoading)
  const setLoading = useAppStore((state) => state.setLoading)
  const error = useAppStore((state) => state.error)
  const setError = useAppStore((state) => state.setError)

  return {
    currentPage,
    setCurrentPage,
    isMobileMenuOpen,
    setMobileMenuOpen,
    isLoading,
    setLoading,
    error,
    setError,
  }
}

export const useSearch = () => {
  const searchFilters = useAppStore((state) => state.searchFilters)
  const setSearchFilters = useAppStore((state) => state.setSearchFilters)
  const clearFilters = useAppStore((state) => state.clearFilters)

  return {
    searchFilters,
    setSearchFilters,
    clearFilters,
  }
}
