globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/auth/register/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/providers/ClientProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/ClientProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(ssr)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/loading.tsx":{"*":{"id":"(ssr)/./src/app/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/creator/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/creator/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/merchant/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/merchant/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/register/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\components\\providers\\ClientProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/ClientProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\error.tsx":{"id":"(app-pages-browser)/./src/app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\loading.tsx":{"id":"(app-pages-browser)/./src/app/loading.tsx","name":"*","chunks":["app/loading","static/chunks/app/loading.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\creator\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/creator/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\merchant\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/merchant/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\auth\\register\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/register/page.tsx","name":"*","chunks":["app/auth/register/page","static/chunks/app/auth/register/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\admin\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\":[],"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\error":[],"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\loading":[],"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\not-found":[],"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\1amshor333a\\influencer-platform\\src\\app\\auth\\register\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/ClientProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/ClientProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(rsc)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/loading.tsx":{"*":{"id":"(rsc)/./src/app/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(rsc)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/creator/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/creator/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/merchant/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/merchant/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/register/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}