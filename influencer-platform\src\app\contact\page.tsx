'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import {
  ArrowLeft,
  MessageCircle,
  Phone,
  Mail,
  MapPin,
  Clock,
  Send,
  CheckCircle
} from 'lucide-react'

export default function ContactPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const contactMethods = [
    {
      icon: Phone,
      title: 'الهاتف',
      value: '+966 11 123 4567',
      description: 'متاح من 9 صباحاً إلى 6 مساءً'
    },
    {
      icon: Mail,
      title: 'البريد الإلكتروني',
      value: '<EMAIL>',
      description: 'نرد خلال 24 ساعة'
    },
    {
      icon: MapPin,
      title: 'العنوان',
      value: 'الرياض، المملكة العربية السعودية',
      description: 'مركز الملك عبدالله المالي'
    },
    {
      icon: Clock,
      title: 'ساعات العمل',
      value: 'الأحد - الخميس',
      description: '9:00 ص - 6:00 م'
    }
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    setIsSubmitting(false)
    setIsSubmitted(true)
  }

  if (isSubmitted) {
    return (
      <MobileLayout>
        <div className="min-h-screen flex items-center justify-center p-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center max-w-md w-full"
          >
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-10 h-10 text-green-600" />
            </div>

            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              تم إرسال رسالتك بنجاح!
            </h1>

            <p className="text-gray-600 mb-8 leading-relaxed">
              شكراً لتواصلك معنا. سيقوم فريق الدعم بالرد عليك خلال 24 ساعة.
            </p>

            <div className="space-y-4">
              <Button
                variant="primary"
                fullWidth
                onClick={() => router.push('/')}
              >
                العودة للصفحة الرئيسية
              </Button>

              <Button
                variant="ghost"
                fullWidth
                onClick={() => setIsSubmitted(false)}
              >
                إرسال رسالة أخرى
              </Button>
            </div>
          </motion.div>
        </div>
      </MobileLayout>
    )
  }

  return (
    <MobileLayout>
      <div className="min-h-screen bg-slate-900">
        <div className="max-w-4xl mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-slate-800 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-slate-400" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-white">اتصل بنا</h1>
              <p className="text-slate-400">نحن هنا لمساعدتك</p>
            </div>
          </div>

        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-8 h-8 text-blue-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                كيف يمكننا مساعدتك؟
              </h2>
              <p className="text-gray-600">
                فريق الدعم جاهز للإجابة على استفساراتك ومساعدتك في استخدام المنصة
              </p>
            </div>
          </Card>
        </motion.div>

        {/* Contact Methods */}
        <div className="grid grid-cols-1 gap-4">
          {contactMethods.map((method, index) => {
            const Icon = method.icon
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
              >
                <Card>
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Icon className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">
                        {method.title}
                      </h3>
                      <p className="text-gray-700 font-medium">
                        {method.value}
                      </p>
                      <p className="text-sm text-gray-500">
                        {method.description}
                      </p>
                    </div>
                  </div>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Contact Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              أرسل لنا رسالة
            </h3>

            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                label="الاسم الكامل"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="أدخل اسمك الكامل"
                required
              />

              <Input
                label="البريد الإلكتروني"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                required
              />

              <Input
                label="الموضوع"
                value={formData.subject}
                onChange={(e) => handleInputChange('subject', e.target.value)}
                placeholder="موضوع الرسالة"
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الرسالة
                </label>
                <textarea
                  value={formData.message}
                  onChange={(e) => handleInputChange('message', e.target.value)}
                  className="input-field min-h-[120px] resize-none"
                  placeholder="اكتب رسالتك هنا..."
                  required
                />
              </div>

              <Button
                type="submit"
                variant="primary"
                size="lg"
                fullWidth
                isLoading={isSubmitting}
                rightIcon={<Send className="w-5 h-5" />}
              >
                إرسال الرسالة
              </Button>
            </form>
          </Card>
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              الأسئلة الشائعة
            </h3>

            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-1">
                  كيف يمكنني إنشاء حساب؟
                </h4>
                <p className="text-sm text-gray-600">
                  اضغط على "إنشاء حساب" واختر نوع المستخدم (تاجر أو مؤثر) واتبع الخطوات.
                </p>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-1">
                  كم تستغرق عملية الدفع؟
                </h4>
                <p className="text-sm text-gray-600">
                  المدفوعات فورية عبر Apple Pay و Google Pay. الأموال محجوزة حتى إتمام الخدمة.
                </p>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-1">
                  هل يمكنني إلغاء طلب؟
                </h4>
                <p className="text-sm text-gray-600">
                  يمكن إلغاء الطلبات قبل موافقة المؤثر. بعد الموافقة، يتطلب الأمر موافقة الطرفين.
                </p>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Emergency Contact */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
        >
          <Card className="bg-red-50 border-red-200">
            <div className="text-center">
              <h3 className="font-semibold text-red-900 mb-2">
                حالة طوارئ؟
              </h3>
              <p className="text-red-700 text-sm mb-4">
                للمشاكل العاجلة أو الطوارئ، يمكنك التواصل معنا مباشرة
              </p>
              <Button variant="outline" size="sm" className="border-red-300 text-red-700">
                اتصال طوارئ
              </Button>
            </div>
          </Card>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
