# منصة المؤثرين السعودية 🇸🇦

منصة احترافية متكاملة لربط التجار مع المؤثرين ومبدعي المحتوى في المملكة العربية السعودية.

## 🌟 المميزات الرئيسية

### للتجار:
- **إنشاء حملات إعلانية** متكاملة مع تحديد المتطلبات والميزانية
- **البحث والفلترة** للعثور على المؤثر المناسب
- **لوحة تحكم شاملة** لمتابعة الحملات والنتائج
- **نظام دفع آمن** مع Apple Pay و Google Pay
- **تتبع الأداء** والحصول على تقارير مفصلة

### للمؤثرين ومبدعي المحتوى:
- **ملف شخصي احترافي** مع إحصائيات المنصات
- **إدارة الخدمات والأسعار** بمرونة كاملة
- **معرض الأعمال** لعرض المحتوى السابق
- **استقبال الطلبات** ومتابعة الحملات
- **نظام تقييمات** لبناء السمعة

### للمشرفين:
- **لوحة تحكم إدارية** شاملة
- **إدارة المستخدمين** والحملات
- **نظام المدفوعات** والعمولات
- **تقارير مالية** مفصلة

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 14 مع App Router
- **Styling**: Tailwind CSS مع تصميم mobile-first
- **Animations**: Framer Motion
- **State Management**: Zustand
- **Forms**: React Hook Form
- **Icons**: Lucide React
- **Language**: TypeScript للأمان والجودة

## 🚀 التشغيل السريع

### المتطلبات:
- Node.js 18+
- npm أو yarn

### خطوات التشغيل:

1. **تثبيت المكتبات**:
```bash
npm install
```

2. **تشغيل المشروع**:
```bash
npm run dev
```

3. **فتح المتصفح**:
افتح [http://localhost:3000](http://localhost:3000)

## 📱 التصميم والواجهة

### Mobile-First Design:
- تصميم يركز على الجوال أولاً
- واجهة تشبه التطبيقات الأصلية
- تجربة مستخدم سلسة ومتجاوبة
- دعم كامل للغة العربية (RTL)

### المكونات الرئيسية:
- **MobileLayout**: تخطيط أساسي للجوال
- **Header**: شريط علوي مع التنقل
- **BottomNavigation**: شريط تنقل سفلي
- **Cards**: بطاقات تفاعلية
- **Buttons**: أزرار متنوعة الأنماط
- **Forms**: نماذج احترافية

## 🗂️ هيكل المشروع

```
src/
├── app/                    # صفحات Next.js
│   ├── auth/              # صفحات المصادقة
│   ├── merchant/          # صفحات التاجر
│   ├── creator/           # صفحات المؤثر
│   ├── admin/             # صفحات المشرف
│   └── search/            # صفحة البحث
├── components/            # المكونات القابلة لإعادة الاستخدام
│   ├── ui/               # مكونات الواجهة الأساسية
│   └── layout/           # مكونات التخطيط
├── lib/                  # المكتبات والأدوات
│   ├── types.ts          # تعريفات TypeScript
│   ├── utils.ts          # دوال مساعدة
│   └── store.ts          # إدارة الحالة
└── styles/               # ملفات التنسيق
```

## 👥 أنواع المستخدمين

### 1. التاجر (Merchant):
- إنشاء وإدارة الحملات
- البحث عن المؤثرين
- متابعة النتائج والتقييمات

### 2. المؤثر (Influencer):
- مؤثرين مشاهير بمتابعين كثر
- خدمات متنوعة عبر المنصات

### 3. مبدع المحتوى (UGC Creator):
- مبدعين غير مشاهير
- محتوى إعلاني احترافي

### 4. المشرف (Admin):
- إدارة شاملة للمنصة
- مراقبة العمليات والمدفوعات

## 🔐 نظام المصادقة

- تسجيل دخول آمن
- تسجيل حسابات جديدة
- تصنيف المستخدمين حسب النوع
- حماية الصفحات حسب الصلاحيات

## 💳 نظام المدفوعات

- دفع آمن عبر Apple Pay و Google Pay
- حجز الأموال حتى إتمام الإعلان
- نظام عمولات للمنصة
- تقارير مالية مفصلة

## 📊 الإحصائيات والتقارير

- إحصائيات المؤثرين (متابعين، تفاعل)
- تحليل الجمهور (عمر، جنس، مدن)
- تقارير أداء الحملات
- مؤشرات الأداء الرئيسية

## 🎨 التخصيص والثيمات

### الألوان الرئيسية:
- **الأخضر**: #10B981 (اللون الأساسي)
- **الأزرق**: #3B82F6 (اللون الثانوي)
- **البنفسجي**: #8B5CF6 (لون مميز)

### الخطوط:
- **Inter**: الخط الأساسي
- دعم كامل للعربية والإنجليزية

## 📱 التحويل لتطبيق جوال

المشروع مصمم ليكون قابل للتحويل إلى تطبيق جوال باستخدام:
- **React Native**
- **Capacitor**
- **PWA** (Progressive Web App)

## 🚀 النشر والاستضافة

### خيارات النشر:
- **Vercel** (موصى به)
- **Netlify**
- **AWS**
- **Azure**

### متطلبات الإنتاج:
- قاعدة بيانات (PostgreSQL/MongoDB)
- خدمة تخزين الملفات (AWS S3)
- خدمة الدفع (Stripe/PayPal)
- خدمة الإشعارات

---

**تم تطوير هذا المشروع بواسطة Augment Agent** 🤖

منصة احترافية متكاملة لخدمة السوق السعودي في مجال التسويق بالمؤثرين.
