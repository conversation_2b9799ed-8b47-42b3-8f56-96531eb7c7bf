'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import {
  MapPin,
  Star,
  Users,
  CheckCircle,
  MessageCircle,
  Share2,
  Heart,
  Eye,
  Calendar,
  DollarSign,
  Camera,
  Play,
  ArrowLeft
} from 'lucide-react'
import { formatNumber, formatPrice, formatDate } from '@/lib/utils'

export default function InfluencerProfilePage() {
  const router = useRouter()
  const params = useParams()
  const { user, isAuthenticated } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  // Mock influencer data - in real app, this would come from API
  const [influencer, setInfluencer] = useState({
    id: '1',
    name: 'سارة أحمد',
    displayName: '<PERSON>',
    avatar: '/avatars/sarah.jpg',
    coverImage: '/covers/sarah-cover.jpg',
    category: 'influencer',
    city: 'الرياض',
    bio: 'مؤثرة في مجال الموضة والجمال، أحب مشاركة أحدث صيحات الموضة والنصائح الجمالية مع متابعيني. أؤمن بأن الجمال الحقيقي يأتي من الثقة بالنفس.',
    followers: {
      snapchat: 150000,
      instagram: 85000,
      tiktok: 200000
    },
    rating: 4.9,
    completedCampaigns: 45,
    responseTime: '2 ساعات',
    completionRate: 98,
    services: [
      {
        id: '1',
        type: 'instagram_reel',
        name: 'ريل إنستغرام',
        price: 2500,
        description: 'ريل احترافي مدته 30-60 ثانية',
        deliveryTime: 3
      },
      {
        id: '2',
        type: 'full_story',
        name: 'ستوري كامل',
        price: 1800,
        description: 'ستوري كامل على سناب شات',
        deliveryTime: 1
      },
      {
        id: '3',
        type: 'single_snap',
        name: 'صورة سناب',
        price: 800,
        description: 'صورة واحدة على سناب شات',
        deliveryTime: 1
      }
    ],
    interests: ['الموضة والأزياء', 'الجمال والعناية', 'نمط الحياة'],
    audienceDemographics: {
      ageGroups: {
        '18-24': 35,
        '25-34': 45,
        '35-44': 15,
        '45+': 5
      },
      gender: {
        female: 75,
        male: 25
      },
      topCities: ['الرياض', 'جدة', 'الدمام', 'مكة المكرمة']
    },
    portfolio: [
      {
        id: '1',
        type: 'image',
        url: '/portfolio/sarah-1.jpg',
        title: 'حملة منتجات العناية',
        platform: 'instagram',
        engagement: 12500,
        createdAt: new Date('2024-01-10')
      },
      {
        id: '2',
        type: 'video',
        url: '/portfolio/sarah-2.mp4',
        thumbnail: '/portfolio/sarah-2-thumb.jpg',
        title: 'مراجعة مجموعة المكياج',
        platform: 'tiktok',
        engagement: 25000,
        createdAt: new Date('2024-01-05')
      }
    ],
    reviews: [
      {
        id: '1',
        merchantName: 'متجر الأناقة',
        rating: 5,
        comment: 'تعامل ممتاز ونتائج رائعة. سارة محترفة جداً وملتزمة بالمواعيد.',
        createdAt: new Date('2024-01-15')
      },
      {
        id: '2',
        merchantName: 'مركز الجمال',
        rating: 5,
        comment: 'أفضل مؤثرة تعاملت معها. المحتوى عالي الجودة والتفاعل ممتاز.',
        createdAt: new Date('2024-01-10')
      }
    ],
    isVerified: true,
    isAvailable: true,
    joinedDate: new Date('2023-06-15')
  })

  useEffect(() => {
    // Simulate loading
    setTimeout(() => setIsLoading(false), 1000)
  }, [params.id])

  if (isLoading) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }

  const totalFollowers = Object.values(influencer.followers).reduce((sum, count) => sum + count, 0)
  const averageEngagement = 8.5 // Mock data

  return (
    <MobileLayout>
      <div className="pb-6">
        {/* Cover Image */}
        <div className="relative h-48 bg-gradient-to-br from-purple-400 to-pink-400">
          <div className="absolute inset-0 bg-black bg-opacity-20" />

          {/* Back Button */}
          <button
            onClick={() => router.back()}
            className="absolute top-4 left-4 w-10 h-10 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>

          {/* Share Button */}
          <button className="absolute top-4 right-4 w-10 h-10 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white">
            <Share2 className="w-5 h-5" />
          </button>
        </div>

        {/* Profile Info */}
        <div className="px-6 -mt-16 relative z-10">
          <div className="flex items-end space-x-4 rtl:space-x-reverse mb-4">
            <div className="relative">
              <div className="w-24 h-24 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-2xl border-4 border-white">
                {influencer.name.charAt(0)}
              </div>
              {influencer.isVerified && (
                <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white">
                  <CheckCircle className="w-5 h-5 text-white" />
                </div>
              )}
            </div>

            <div className="flex-1 pb-2">
              <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                <h1 className="text-xl font-bold text-gray-900">
                  {influencer.name}
                </h1>
                <Badge
                  variant={influencer.category === 'influencer' ? 'info' : 'default'}
                  size="sm"
                >
                  {influencer.category === 'influencer' ? 'مؤثر' : 'مبدع محتوى'}
                </Badge>
              </div>

              <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600">
                <MapPin className="w-4 h-4" />
                <span>{influencer.city}</span>
                <span>•</span>
                <Star className="w-4 h-4 text-yellow-500" />
                <span>{influencer.rating}</span>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="text-lg font-bold text-gray-900">
                {formatNumber(totalFollowers)}
              </div>
              <div className="text-sm text-gray-600">متابع</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-gray-900">
                {influencer.completedCampaigns}
              </div>
              <div className="text-sm text-gray-600">حملة</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-gray-900">
                {averageEngagement}%
              </div>
              <div className="text-sm text-gray-600">تفاعل</div>
            </div>
          </div>

          {/* Action Buttons */}
          {isAuthenticated && user?.type === 'merchant' && (
            <div className="flex space-x-3 rtl:space-x-reverse mb-6">
              <Button
                variant="primary"
                className="flex-1"
                leftIcon={<MessageCircle className="w-5 h-5" />}
                onClick={() => router.push(`/influencer/${influencer.id}/contact`)}
              >
                تواصل
              </Button>
              <Button
                variant="outline"
                leftIcon={<Heart className="w-5 h-5" />}
              >
                حفظ
              </Button>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="px-6 mb-6">
          <div className="flex space-x-1 rtl:space-x-reverse bg-gray-100 rounded-lg p-1">
            {[
              { id: 'overview', label: 'نظرة عامة' },
              { id: 'services', label: 'الخدمات' },
              { id: 'portfolio', label: 'المعرض' },
              { id: 'reviews', label: 'التقييمات' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-white text-green-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <div className="px-6 space-y-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* Bio */}
              <Card>
                <h3 className="font-semibold text-gray-900 mb-3">نبذة تعريفية</h3>
                <p className="text-gray-600 leading-relaxed">{influencer.bio}</p>
              </Card>

              {/* Platform Stats */}
              <Card>
                <h3 className="font-semibold text-gray-900 mb-4">إحصائيات المنصات</h3>
                <div className="space-y-3">
                  {Object.entries(influencer.followers).map(([platform, count]) => (
                    <div key={platform} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-purple-500 rounded-lg flex items-center justify-center">
                          <span className="text-white text-sm">
                            {platform === 'snapchat' ? '👻' :
                             platform === 'instagram' ? '📷' : '🎵'}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 capitalize">
                            {platform}
                          </div>
                          <div className="text-sm text-gray-600">
                            {formatNumber(count)} متابع
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-green-600 font-medium">
                        نشط
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {/* Audience Demographics */}
              <Card>
                <h3 className="font-semibold text-gray-900 mb-4">تحليل الجمهور</h3>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">الفئات العمرية</h4>
                    <div className="space-y-2">
                      {Object.entries(influencer.audienceDemographics.ageGroups).map(([age, percentage]) => (
                        <div key={age} className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">{age} سنة</span>
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <div className="w-20 h-2 bg-gray-200 rounded-full">
                              <div
                                className="h-full bg-green-500 rounded-full"
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium text-gray-900 w-8">
                              {percentage}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">الجنس</h4>
                    <div className="flex space-x-4 rtl:space-x-reverse">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-pink-500 rounded-full mr-2" />
                        <span className="text-sm text-gray-600">
                          إناث {influencer.audienceDemographics.gender.female}%
                        </span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-blue-500 rounded-full mr-2" />
                        <span className="text-sm text-gray-600">
                          ذكور {influencer.audienceDemographics.gender.male}%
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">أهم المدن</h4>
                    <div className="flex flex-wrap gap-2">
                      {influencer.audienceDemographics.topCities.map(city => (
                        <Badge key={city} variant="default" size="sm">
                          {city}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          )}

          {/* Services Tab */}
          {activeTab === 'services' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              {influencer.services.map((service, index) => (
                <motion.div
                  key={service.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card>
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-1">
                          {service.name}
                        </h3>
                        <p className="text-sm text-gray-600 mb-2">
                          {service.description}
                        </p>
                        <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500">
                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            <span>{service.deliveryTime} أيام</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-green-600">
                          {formatPrice(service.price)}
                        </div>
                      </div>
                    </div>

                    {isAuthenticated && user?.type === 'merchant' && (
                      <Button
                        variant="outline"
                        size="sm"
                        fullWidth
                        onClick={() => router.push(`/influencer/${influencer.id}/contact?service=${service.id}`)}
                      >
                        طلب هذه الخدمة
                      </Button>
                    )}
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          )}

          {/* Portfolio Tab */}
          {activeTab === 'portfolio' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              {influencer.portfolio.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card>
                    <div className="flex items-start space-x-3 rtl:space-x-reverse">
                      <div className="relative w-20 h-20 bg-gray-100 rounded-lg flex-shrink-0">
                        {item.type === 'video' ? (
                          <div className="w-full h-full bg-gradient-to-br from-purple-400 to-pink-400 rounded-lg flex items-center justify-center">
                            <Play className="w-8 h-8 text-white" />
                          </div>
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-400 rounded-lg flex items-center justify-center">
                            <Camera className="w-8 h-8 text-white" />
                          </div>
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 mb-1">
                          {item.title}
                        </h3>
                        <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 mb-2">
                          <span className="capitalize">{item.platform}</span>
                          <span>•</span>
                          <span>{formatDate(item.createdAt)}</span>
                        </div>
                        <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm">
                          <div className="flex items-center text-gray-500">
                            <Eye className="w-4 h-4 mr-1" />
                            <span>{formatNumber(item.engagement)}</span>
                          </div>
                          <Badge variant="default" size="sm">
                            {item.type === 'video' ? 'فيديو' : 'صورة'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          )}

          {/* Reviews Tab */}
          {activeTab === 'reviews' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <div className="text-center mb-6">
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  {influencer.rating}
                </div>
                <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse mb-2">
                  {[1, 2, 3, 4, 5].map(star => (
                    <Star
                      key={star}
                      className={`w-5 h-5 ${
                        star <= Math.floor(influencer.rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <div className="text-sm text-gray-600">
                  بناءً على {influencer.reviews.length} تقييم
                </div>
              </div>

              {influencer.reviews.map((review, index) => (
                <motion.div
                  key={review.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card>
                    <div className="flex items-start space-x-3 rtl:space-x-reverse">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                        {review.merchantName.charAt(0)}
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium text-gray-900">
                            {review.merchantName}
                          </h3>
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            {[1, 2, 3, 4, 5].map(star => (
                              <Star
                                key={star}
                                className={`w-4 h-4 ${
                                  star <= review.rating
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                        </div>

                        <p className="text-gray-600 text-sm mb-2">
                          {review.comment}
                        </p>

                        <div className="text-xs text-gray-500">
                          {formatDate(review.createdAt)}
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          )}
        </div>
      </div>
    </MobileLayout>
  )
}
