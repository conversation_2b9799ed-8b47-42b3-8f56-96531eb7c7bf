'use client'

import React, { useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Button from '@/components/ui/Button'
import { RefreshCw, Home, AlertTriangle } from 'lucide-react'

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {
  const router = useRouter()

  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application Error:', error)
  }, [error])

  return (
    <MobileLayout showHeader={false} showBottomNav={false}>
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center max-w-md w-full"
        >
          {/* Logo */}
          <div className="w-20 h-20 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <AlertTriangle className="w-10 h-10 text-white" />
          </div>
          
          {/* Error Icon */}
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-6xl mb-4"
          >
            ⚠️
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              حدث خطأ غير متوقع
            </h1>
            
            <p className="text-gray-600 mb-2 leading-relaxed">
              عذراً، حدث خطأ أثناء تحميل هذه الصفحة.
            </p>
            
            {process.env.NODE_ENV === 'development' && (
              <details className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-left">
                <summary className="cursor-pointer text-red-700 font-medium mb-2">
                  تفاصيل الخطأ (للمطورين)
                </summary>
                <pre className="text-xs text-red-600 overflow-auto">
                  {error.message}
                  {error.stack && (
                    <>
                      {'\n\n'}
                      {error.stack}
                    </>
                  )}
                </pre>
              </details>
            )}
            
            <div className="space-y-4">
              <Button
                variant="primary"
                size="lg"
                fullWidth
                onClick={reset}
                leftIcon={<RefreshCw className="w-5 h-5" />}
              >
                إعادة المحاولة
              </Button>
              
              <Button
                variant="secondary"
                size="lg"
                fullWidth
                onClick={() => router.push('/')}
                leftIcon={<Home className="w-5 h-5" />}
              >
                العودة للصفحة الرئيسية
              </Button>
              
              <Button
                variant="ghost"
                size="lg"
                fullWidth
                onClick={() => window.location.reload()}
              >
                إعادة تحميل الصفحة
              </Button>
            </div>
          </motion.div>
          
          {/* Help Text */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg"
          >
            <p className="text-sm text-blue-700">
              💡 إذا استمر الخطأ، يرجى تحديث الصفحة أو المحاولة لاحقاً.
              يمكنك أيضاً التواصل مع الدعم الفني إذا لزم الأمر.
            </p>
          </motion.div>
          
          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="mt-8 text-center"
          >
            <p className="text-xs text-gray-500">
              منصة المؤثرين السعودية
            </p>
          </motion.div>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
