'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import MobileLayout from '@/components/layout/MobileLayout'
import Button from '@/components/ui/Button'
import { Home, ArrowLeft, Search } from 'lucide-react'

export default function NotFound() {
  const router = useRouter()

  return (
    <MobileLayout showHeader={false} showBottomNav={false}>
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center max-w-md w-full"
        >
          {/* Logo */}
          <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <span className="text-white text-3xl">🇸🇦</span>
          </div>
          
          {/* 404 Animation */}
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-8xl font-bold text-gray-300 mb-4"
          >
            404
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              الصفحة غير موجودة
            </h1>
            
            <p className="text-gray-600 mb-8 leading-relaxed">
              عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
              يمكنك العودة إلى الصفحة الرئيسية أو البحث عن ما تريد.
            </p>
            
            <div className="space-y-4">
              <Button
                variant="primary"
                size="lg"
                fullWidth
                onClick={() => router.push('/')}
                leftIcon={<Home className="w-5 h-5" />}
              >
                العودة للصفحة الرئيسية
              </Button>
              
              <Button
                variant="secondary"
                size="lg"
                fullWidth
                onClick={() => router.push('/search')}
                leftIcon={<Search className="w-5 h-5" />}
              >
                البحث عن المؤثرين
              </Button>
              
              <Button
                variant="ghost"
                size="lg"
                fullWidth
                onClick={() => router.back()}
                leftIcon={<ArrowLeft className="w-5 h-5" />}
              >
                العودة للصفحة السابقة
              </Button>
            </div>
          </motion.div>
          
          {/* Decorative Elements */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="mt-12 text-center"
          >
            <div className="flex justify-center space-x-4 rtl:space-x-reverse text-gray-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }} />
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }} />
            </div>
            
            <p className="text-xs text-gray-500 mt-4">
              منصة المؤثرين السعودية
            </p>
          </motion.div>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
