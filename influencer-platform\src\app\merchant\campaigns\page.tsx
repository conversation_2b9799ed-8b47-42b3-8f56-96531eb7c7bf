'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { 
  ArrowLeft,
  Plus,
  Filter,
  Search,
  Calendar,
  DollarSign,
  Eye,
  MessageCircle,
  Edit3,
  Trash2,
  Clock,
  CheckCircle,
  X,
  AlertCircle
} from 'lucide-react'
import { formatPrice, formatDate } from '@/lib/utils'

interface Campaign {
  id: string
  title: string
  description: string
  budget: number
  status: 'draft' | 'pending' | 'in_progress' | 'completed' | 'cancelled'
  deadline: string
  createdAt: string
  influencer?: {
    id: string
    name: string
    avatar: string
  }
  applications: number
  category: string
  requirements: string[]
}

export default function CampaignsPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [showFilters, setShowFilters] = useState(false)

  // Mock campaigns data
  const [campaigns] = useState<Campaign[]>([
    {
      id: '1',
      title: 'إعلان منتج العناية بالبشرة',
      description: 'حملة لترويج منتج جديد للعناية بالبشرة موجه للنساء',
      budget: 5000,
      status: 'in_progress',
      deadline: '2024-02-15',
      createdAt: '2024-01-10',
      influencer: {
        id: '1',
        name: 'سارة أحمد',
        avatar: ''
      },
      applications: 12,
      category: 'beauty',
      requirements: ['ريل إنستغرام', 'ستوري كامل', 'تجربة المنتج']
    },
    {
      id: '2',
      title: 'مراجعة تطبيق التوصيل',
      description: 'نبحث عن مؤثر لمراجعة تطبيق التوصيل الجديد',
      budget: 3500,
      status: 'pending',
      deadline: '2024-02-20',
      createdAt: '2024-01-15',
      applications: 8,
      category: 'technology',
      requirements: ['فيديو يوتيوب', 'ريل إنستغرام', 'سناب شات']
    },
    {
      id: '3',
      title: 'إطلاق مجموعة أزياء جديدة',
      description: 'حملة لإطلاق مجموعة الأزياء الصيفية الجديدة',
      budget: 7500,
      status: 'completed',
      deadline: '2024-01-30',
      createdAt: '2024-01-05',
      influencer: {
        id: '2',
        name: 'نورا خالد',
        avatar: ''
      },
      applications: 15,
      category: 'fashion',
      requirements: ['صور إنستغرام', 'ستوري', 'ريل']
    },
    {
      id: '4',
      title: 'مسودة حملة المطعم',
      description: 'حملة لترويج مطعم جديد في الرياض',
      budget: 2500,
      status: 'draft',
      deadline: '2024-03-01',
      createdAt: '2024-01-20',
      applications: 0,
      category: 'food',
      requirements: ['زيارة المطعم', 'فيديو تيك توك']
    }
  ])

  const [filteredCampaigns, setFilteredCampaigns] = useState(campaigns)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, router])

  useEffect(() => {
    let filtered = campaigns

    if (selectedStatus !== 'all') {
      filtered = filtered.filter(campaign => campaign.status === selectedStatus)
    }

    setFilteredCampaigns(filtered)
  }, [selectedStatus, campaigns])

  const getStatusText = (status: string) => {
    switch (status) {
      case 'draft': return 'مسودة'
      case 'pending': return 'في الانتظار'
      case 'in_progress': return 'قيد التنفيذ'
      case 'completed': return 'مكتملة'
      case 'cancelled': return 'ملغية'
      default: return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'default'
      case 'pending': return 'warning'
      case 'in_progress': return 'info'
      case 'completed': return 'success'
      case 'cancelled': return 'error'
      default: return 'default'
    }
  }

  const handleDelete = (campaignId: string) => {
    if (confirm('هل أنت متأكد من حذف هذه الحملة؟')) {
      alert('تم حذف الحملة بنجاح')
    }
  }

  const handlePublish = (campaignId: string) => {
    alert('تم نشر الحملة بنجاح!')
  }

  if (!user) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">حملاتي</h1>
              <p className="text-gray-600">{filteredCampaigns.length} حملة</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Button
              variant={showFilters ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              leftIcon={<Filter className="w-4 h-4" />}
            >
              فلتر
            </Button>
            <Button
              variant="primary"
              size="sm"
              onClick={() => router.push('/merchant/create-campaign')}
              leftIcon={<Plus className="w-4 h-4" />}
            >
              جديدة
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="grid grid-cols-4 gap-3">
            <Card className="text-center p-3">
              <div className="text-lg font-bold text-gray-900">
                {campaigns.length}
              </div>
              <div className="text-xs text-gray-600">إجمالي</div>
            </Card>
            <Card className="text-center p-3">
              <div className="text-lg font-bold text-blue-600">
                {campaigns.filter(c => c.status === 'in_progress').length}
              </div>
              <div className="text-xs text-gray-600">نشطة</div>
            </Card>
            <Card className="text-center p-3">
              <div className="text-lg font-bold text-green-600">
                {campaigns.filter(c => c.status === 'completed').length}
              </div>
              <div className="text-xs text-gray-600">مكتملة</div>
            </Card>
            <Card className="text-center p-3">
              <div className="text-lg font-bold text-orange-600">
                {campaigns.filter(c => c.status === 'draft').length}
              </div>
              <div className="text-xs text-gray-600">مسودات</div>
            </Card>
          </div>
        </motion.div>

        {/* Filters */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <Card>
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">فلترة الحملات</h3>
                <button
                  onClick={() => setShowFilters(false)}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  <X className="w-4 h-4 text-gray-500" />
                </button>
              </div>

              <div className="grid grid-cols-2 gap-2">
                {[
                  { key: 'all', label: 'الكل' },
                  { key: 'draft', label: 'مسودات' },
                  { key: 'pending', label: 'في الانتظار' },
                  { key: 'in_progress', label: 'نشطة' },
                  { key: 'completed', label: 'مكتملة' },
                  { key: 'cancelled', label: 'ملغية' }
                ].map((filter) => (
                  <button
                    key={filter.key}
                    onClick={() => setSelectedStatus(filter.key)}
                    className={`p-3 rounded-lg border text-sm transition-all ${
                      selectedStatus === filter.key
                        ? 'border-green-500 bg-green-50 text-green-600'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {filter.label}
                  </button>
                ))}
              </div>
            </Card>
          </motion.div>
        )}

        {/* Campaigns List */}
        <div className="space-y-4">
          {filteredCampaigns.map((campaign, index) => (
            <motion.div
              key={campaign.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 + index * 0.1 }}
            >
              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                        <h3 className="font-semibold text-gray-900 truncate">
                          {campaign.title}
                        </h3>
                        <Badge
                          variant={getStatusColor(campaign.status) as any}
                          size="sm"
                        >
                          {getStatusText(campaign.status)}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {campaign.description}
                      </p>
                      
                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500">
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <DollarSign className="w-4 h-4" />
                          <span>{formatPrice(campaign.budget)}</span>
                        </div>
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(campaign.deadline)}</span>
                        </div>
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <Eye className="w-4 h-4" />
                          <span>{campaign.applications} متقدم</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/merchant/campaigns/${campaign.id}`)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      
                      {campaign.status === 'draft' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => router.push(`/merchant/campaigns/${campaign.id}/edit`)}
                        >
                          <Edit3 className="w-4 h-4" />
                        </Button>
                      )}
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(campaign.id)}
                        className="text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Influencer Info */}
                  {campaign.influencer && (
                    <div className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center text-white font-bold">
                        {campaign.influencer.name.charAt(0)}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-green-900">
                          {campaign.influencer.name}
                        </div>
                        <div className="text-sm text-green-700">
                          المؤثر المختار
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/influencer/${campaign.influencer?.id}`)}
                      >
                        <MessageCircle className="w-4 h-4" />
                      </Button>
                    </div>
                  )}

                  {/* Requirements */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">المتطلبات:</h4>
                    <div className="flex flex-wrap gap-2">
                      {campaign.requirements.map((req, idx) => (
                        <Badge key={idx} variant="default" size="sm">
                          {req}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  {campaign.status === 'draft' && (
                    <div className="flex space-x-3 rtl:space-x-reverse pt-3 border-t border-gray-100">
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => handlePublish(campaign.id)}
                        leftIcon={<CheckCircle className="w-4 h-4" />}
                        className="flex-1"
                      >
                        نشر الحملة
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/merchant/campaigns/${campaign.id}/edit`)}
                        leftIcon={<Edit3 className="w-4 h-4" />}
                        className="flex-1"
                      >
                        تعديل
                      </Button>
                    </div>
                  )}

                  {/* Status Indicators */}
                  {campaign.status === 'pending' && campaign.applications > 0 && (
                    <div className="flex items-center space-x-2 rtl:space-x-reverse p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <AlertCircle className="w-5 h-5 text-blue-600" />
                      <span className="text-sm text-blue-800">
                        لديك {campaign.applications} طلب جديد للمراجعة
                      </span>
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {filteredCampaigns.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="text-gray-400 text-6xl mb-4">📋</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              لا توجد حملات
            </h3>
            <p className="text-gray-600 mb-4">
              ابدأ بإنشاء حملتك الأولى للوصول للمؤثرين
            </p>
            <Button
              variant="primary"
              onClick={() => router.push('/merchant/create-campaign')}
              leftIcon={<Plus className="w-5 h-5" />}
            >
              إنشاء حملة جديدة
            </Button>
          </motion.div>
        )}
      </div>
    </MobileLayout>
  )
}
