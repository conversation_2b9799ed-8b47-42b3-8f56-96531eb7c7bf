'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks'
import MobileLayout from '@/components/layout/MobileLayout'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  ArrowLeft,
  Bell,
  Shield,
  Moon,
  Globe,
  CreditCard,
  HelpCircle,
  LogOut,
  Trash2,
  Eye,
  EyeOff,
  Smartphone,
  Mail,
  MessageSquare
} from 'lucide-react'

export default function SettingsPage() {
  const router = useRouter()
  const { user, isAuthenticated, logout } = useAuth()
  
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      sms: true,
      push: true,
      marketing: false
    },
    privacy: {
      profileVisible: true,
      showEmail: false,
      showPhone: false
    },
    preferences: {
      darkMode: false,
      language: 'ar'
    }
  })

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, router])

  const toggleSetting = (category: string, setting: string) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [setting]: !prev[category as keyof typeof prev][setting as keyof typeof prev[category as keyof typeof prev]]
      }
    }))
  }

  const handleLogout = () => {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      logout()
      router.push('/auth/login')
    }
  }

  const handleDeleteAccount = () => {
    if (confirm('هل أنت متأكد من حذف الحساب؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      // Handle account deletion
      alert('سيتم حذف حسابك خلال 24 ساعة. يمكنك إلغاء هذا الطلب بتسجيل الدخول مرة أخرى.')
      logout()
      router.push('/')
    }
  }

  if (!user) {
    return (
      <MobileLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading-spinner" />
        </div>
      </MobileLayout>
    )
  }

  const settingSections = [
    {
      title: 'الإشعارات',
      icon: Bell,
      items: [
        {
          key: 'email',
          label: 'إشعارات البريد الإلكتروني',
          description: 'تلقي إشعارات عبر البريد الإلكتروني',
          icon: Mail,
          value: settings.notifications.email
        },
        {
          key: 'sms',
          label: 'الرسائل النصية',
          description: 'تلقي إشعارات عبر الرسائل النصية',
          icon: Smartphone,
          value: settings.notifications.sms
        },
        {
          key: 'push',
          label: 'الإشعارات الفورية',
          description: 'تلقي إشعارات فورية في المتصفح',
          icon: Bell,
          value: settings.notifications.push
        },
        {
          key: 'marketing',
          label: 'الإشعارات التسويقية',
          description: 'تلقي عروض وأخبار المنصة',
          icon: MessageSquare,
          value: settings.notifications.marketing
        }
      ]
    },
    {
      title: 'الخصوصية',
      icon: Shield,
      items: [
        {
          key: 'profileVisible',
          label: 'إظهار الملف الشخصي',
          description: 'السماح للآخرين برؤية ملفك الشخصي',
          icon: Eye,
          value: settings.privacy.profileVisible
        },
        {
          key: 'showEmail',
          label: 'إظهار البريد الإلكتروني',
          description: 'عرض البريد الإلكتروني في الملف العام',
          icon: Mail,
          value: settings.privacy.showEmail
        },
        {
          key: 'showPhone',
          label: 'إظهار رقم الجوال',
          description: 'عرض رقم الجوال في الملف العام',
          icon: Smartphone,
          value: settings.privacy.showPhone
        }
      ]
    }
  ]

  return (
    <MobileLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">الإعدادات</h1>
            <p className="text-gray-600">إدارة تفضيلات حسابك</p>
          </div>
        </div>

        {/* Settings Sections */}
        {settingSections.map((section, sectionIndex) => {
          const SectionIcon = section.icon
          return (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 + sectionIndex * 0.1 }}
            >
              <Card>
                <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <SectionIcon className="w-5 h-5 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {section.title}
                  </h3>
                </div>

                <div className="space-y-4">
                  {section.items.map((item, itemIndex) => {
                    const ItemIcon = item.icon
                    const category = section.title === 'الإشعارات' ? 'notifications' : 'privacy'
                    
                    return (
                      <div
                        key={item.key}
                        className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
                      >
                        <div className="flex items-center space-x-3 rtl:space-x-reverse flex-1">
                          <ItemIcon className="w-5 h-5 text-gray-400" />
                          <div>
                            <div className="font-medium text-gray-900">
                              {item.label}
                            </div>
                            <div className="text-sm text-gray-500">
                              {item.description}
                            </div>
                          </div>
                        </div>
                        
                        <button
                          onClick={() => toggleSetting(category, item.key)}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            item.value ? 'bg-green-600' : 'bg-gray-200'
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              item.value ? 'translate-x-6 rtl:-translate-x-6' : 'translate-x-1 rtl:-translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    )
                  })}
                </div>
              </Card>
            </motion.div>
          )
        })}

        {/* Preferences */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Globe className="w-5 h-5 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                التفضيلات
              </h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Moon className="w-5 h-5 text-gray-400" />
                  <div>
                    <div className="font-medium text-gray-900">الوضع الليلي</div>
                    <div className="text-sm text-gray-500">تفعيل المظهر الداكن</div>
                  </div>
                </div>
                
                <button
                  onClick={() => toggleSetting('preferences', 'darkMode')}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.preferences.darkMode ? 'bg-green-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.preferences.darkMode ? 'translate-x-6 rtl:-translate-x-6' : 'translate-x-1 rtl:-translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Globe className="w-5 h-5 text-gray-400" />
                  <div>
                    <div className="font-medium text-gray-900">اللغة</div>
                    <div className="text-sm text-gray-500">العربية</div>
                  </div>
                </div>
                
                <Button variant="ghost" size="sm">
                  تغيير
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Account Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              إجراءات الحساب
            </h3>
            
            <div className="space-y-3">
              <Button
                variant="outline"
                fullWidth
                leftIcon={<CreditCard className="w-5 h-5" />}
                onClick={() => router.push('/payment-methods')}
              >
                طرق الدفع
              </Button>
              
              <Button
                variant="outline"
                fullWidth
                leftIcon={<HelpCircle className="w-5 h-5" />}
                onClick={() => router.push('/help')}
              >
                المساعدة والدعم
              </Button>
              
              <Button
                variant="outline"
                fullWidth
                leftIcon={<LogOut className="w-5 h-5" />}
                onClick={handleLogout}
                className="text-orange-600 border-orange-200 hover:bg-orange-50"
              >
                تسجيل الخروج
              </Button>
              
              <Button
                variant="outline"
                fullWidth
                leftIcon={<Trash2 className="w-5 h-5" />}
                onClick={handleDeleteAccount}
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                حذف الحساب
              </Button>
            </div>
          </Card>
        </motion.div>

        {/* App Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="bg-gray-50">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600 text-xl">🇸🇦</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">
                منصة المؤثرين السعودية
              </h4>
              <p className="text-sm text-gray-600 mb-2">
                الإصدار 1.0.0
              </p>
              <div className="flex justify-center space-x-4 rtl:space-x-reverse text-xs text-gray-500">
                <button onClick={() => router.push('/privacy')}>
                  سياسة الخصوصية
                </button>
                <button onClick={() => router.push('/terms')}>
                  الشروط والأحكام
                </button>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </MobileLayout>
  )
}
